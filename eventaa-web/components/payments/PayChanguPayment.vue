<template>
  <div class="bg-white dark:bg-zinc-900 p-6 space-y-6">
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Complete Payment
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mt-1">
        Choose your preferred payment method
      </p>
    </div>

    <div v-if="loadingOperators" class="flex flex-col items-center justify-center py-8">
      <CoreLoader :height="40" :width="40" color="red" />
      <span class="ml-2 mt-2 text-gray-600 dark:text-zinc-200"
        >Loading payment methods...</span
      >
    </div>

    <div
      v-else-if="operatorsError"
      class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4"
    >
      <div class="flex items-center space-x-2">
        <Icon
          icon="heroicons:exclamation-triangle-20-solid"
          class="w-5 h-5 text-red-600 dark:text-red-400"
        />
        <span class="text-sm font-medium text-red-800 dark:text-red-200">
          Failed to load mobile money operators
        </span>
      </div>
      <p class="text-sm text-red-700 dark:text-red-300 mt-1">
        {{ operatorsError }}
      </p>
      <button
        @click="fetchOperators"
        class="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
      >
        Try again
      </button>
    </div>

    <div v-else class="space-y-4">
      <!-- Payment Method Selection -->
      <div class="space-y-3">
        <h4 class="font-medium text-gray-700 dark:text-gray-300">
          Choose Payment Method
        </h4>

        <div class="grid grid-cols-1 gap-3">
          <label class="relative flex items-center p-3 border border-gray-200 dark:border-zinc-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors">
            <input
              v-model="selectedPaymentMethod"
              type="radio"
              value="mobile_money"
              class="sr-only"
            />
            <div class="flex items-center space-x-3 flex-1">
              <Icon icon="heroicons:device-phone-mobile" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">Mobile Money</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pay with Airtel Money or TNM Mpamba</div>
              </div>
            </div>
            <div v-if="selectedPaymentMethod === 'mobile_money'" class="w-5 h-5 text-red-600">
              <Icon icon="heroicons:check-circle-20-solid" />
            </div>
          </label>

          <!-- Card Payment Option - Only show if not in development or if banks loaded successfully -->
          <label
            v-if="!banksError"
            class="relative flex items-center p-3 border border-gray-200 dark:border-zinc-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
          >
            <input
              v-model="selectedPaymentMethod"
              type="radio"
              value="card"
              class="sr-only"
            />
            <div class="flex items-center space-x-3 flex-1">
              <Icon icon="heroicons:credit-card" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">Card Payment</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pay with your debit or credit card</div>
              </div>
            </div>
            <div v-if="selectedPaymentMethod === 'card'" class="w-5 h-5 text-red-600">
              <Icon icon="heroicons:check-circle-20-solid" />
            </div>
          </label>

          <!-- Bank Account Option - Only show if banks are available -->
          <label
            v-if="banks.length > 0"
            class="relative flex items-center p-3 border border-gray-200 dark:border-zinc-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
          >
            <input
              v-model="selectedPaymentMethod"
              type="radio"
              value="bank_account"
              class="sr-only"
            />
            <div class="flex items-center space-x-3 flex-1">
              <Icon icon="heroicons:building-library" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">Bank Account</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Pay directly from your bank account</div>
              </div>
            </div>
            <div v-if="selectedPaymentMethod === 'bank_account'" class="w-5 h-5 text-red-600">
              <Icon icon="heroicons:check-circle-20-solid" />
            </div>
          </label>
        </div>
      </div>

      <!-- Mobile Money Operators -->
      <div v-if="selectedPaymentMethod === 'mobile_money'" class="space-y-3">
        <h4 class="font-medium text-gray-700 dark:text-gray-300">
          Select Mobile Money Provider
        </h4>

        <label
          v-for="operator in mobileMoneyOperators"
          :key="operator.id"
          class="relative flex items-center p-4 border border-gray-200 dark:border-zinc-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800 transition-colors"
        >
          <input
            v-model="selectedOperator"
            type="radio"
            :value="operator"
            class="sr-only hidden"
          />
          <div class="flex items-center space-x-3 flex-1">
            <div class="w-16 h-16 flex items-center justify-center">
              <img
                v-if="getOperatorLogo(operator.name)"
                :src="getOperatorLogo(operator.name)"
                class="object-cover w-16 h-16"
                :alt="`${operator.name} logo`"
              />
              <Icon
                v-else
                icon="heroicons:device-phone-mobile"
                class="w-8 h-8 text-gray-400"
              />
            </div>
            <div>
              <div class="text-base font-medium text-gray-900 dark:text-white">
                {{ operator.name }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Pay with your {{ operator.name }} wallet
              </div>
              <div class="text-xs text-gray-400 dark:text-gray-500">
                {{ operator.currency }}
              </div>
            </div>
          </div>
          <div
            v-if="selectedOperator?.id === operator.id"
            class="w-5 h-5 text-red-600"
          >
            <Icon icon="heroicons:check-circle-20-solid" />
          </div>
        </label>

        <div v-if="mobileMoneyOperators.length === 0" class="text-center py-4">
          <p class="text-gray-500 dark:text-gray-400">
            No mobile money operators available
          </p>
        </div>
      </div>

      <!-- Mobile Money Phone Number -->
      <div v-if="selectedPaymentMethod === 'mobile_money' && selectedOperator" class="space-y-2">
        <div class="relative">
          <FormKit
            type="tel"
            v-model="phoneNumber"
            label="Phone Number"
            placeholder="Enter your phone number"
            prefixIcon="phone"
            validation="required"
            help="Do not add zero or country code (265)"
          />
          <p v-if="phoneError" class="text-sm text-red-600 dark:text-red-400">
            {{ phoneError }}
          </p>
        </div>
      </div>

      <!-- Card Details Form -->
      <div v-if="selectedPaymentMethod === 'card'" class="space-y-4">
        <h4 class="font-medium text-gray-700 dark:text-gray-300">
          Card Details
        </h4>

        <div class="grid grid-cols-1 gap-4">
          <div>
            <FormKit
              type="text"
              v-model="cardDetails.number"
              label="Card Number"
              placeholder="1234 5678 9012 3456"
              validation="required"
            />
            <p v-if="cardErrors.number" class="text-sm text-red-600 dark:text-red-400">
              {{ cardErrors.number }}
            </p>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <FormKit
                type="text"
                v-model="cardDetails.expiry_month"
                label="Month"
                placeholder="MM"
                validation="required"
                maxlength="2"
              />
              <p v-if="cardErrors.expiry_month" class="text-sm text-red-600 dark:text-red-400">
                {{ cardErrors.expiry_month }}
              </p>
            </div>

            <div>
              <FormKit
                type="text"
                v-model="cardDetails.expiry_year"
                label="Year"
                placeholder="YYYY"
                validation="required"
                maxlength="4"
              />
              <p v-if="cardErrors.expiry_year" class="text-sm text-red-600 dark:text-red-400">
                {{ cardErrors.expiry_year }}
              </p>
            </div>
          </div>

          <div>
            <FormKit
              type="password"
              v-model="cardDetails.cvv"
              label="CVV"
              placeholder="123"
              validation="required"
              maxlength="4"
            />
            <p v-if="cardErrors.cvv" class="text-sm text-red-600 dark:text-red-400">
              {{ cardErrors.cvv }}
            </p>
          </div>
        </div>
      </div>

      <!-- Bank Account Form -->
      <div v-if="selectedPaymentMethod === 'bank_account'" class="space-y-4">
        <h4 class="font-medium text-gray-700 dark:text-gray-300">
          Bank Account Details
        </h4>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Bank
            </label>
            <select
              v-model="selectedBank"
              class="w-full p-3 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">Choose a bank</option>
              <option v-for="bank in banks" :key="bank.uuid" :value="bank">
                {{ bank.name }}
              </option>
            </select>
          </div>

          <div>
            <FormKit
              type="text"
              v-model="bankAccountDetails.account_number"
              label="Account Number"
              placeholder="Enter your account number"
              validation="required"
            />
            <p v-if="bankAccountErrors.account_number" class="text-sm text-red-600 dark:text-red-400">
              {{ bankAccountErrors.account_number }}
            </p>
          </div>

          <div>
            <FormKit
              type="text"
              v-model="bankAccountDetails.account_name"
              label="Account Name"
              placeholder="Enter account holder name"
              validation="required"
            />
            <p v-if="bankAccountErrors.account_name" class="text-sm text-red-600 dark:text-red-400">
              {{ bankAccountErrors.account_name }}
            </p>
          </div>
        </div>
      </div>

      <div
        v-if="showTimeoutWarning && processing"
        class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 space-y-2"
      >
        <div class="flex items-center space-x-2">
          <Icon
            icon="heroicons:exclamation-triangle-20-solid"
            class="w-5 h-5 text-yellow-600 dark:text-yellow-400"
          />
          <span
            class="text-sm font-medium text-yellow-800 dark:text-yellow-200"
          >
            Payment will timeout soon
          </span>
        </div>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">
          Time remaining: {{ formatTime(timeRemaining) }}
        </p>
      </div>

      <div class="bg-gray-50 dark:bg-zinc-800 p-4 space-y-2">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">Amount:</span>
          <span class="font-medium text-gray-900 dark:text-white">{{
            formatCurrency(amount)
          }}</span>
        </div>
        <div v-if="fees > 0" class="flex justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">Processing Fee:</span>
          <span class="font-medium text-gray-900 dark:text-white">{{
            formatCurrency(fees)
          }}</span>
        </div>
        <div
          class="border-t border-gray-200 dark:border-zinc-700 pt-2 flex justify-between"
        >
          <span class="font-medium text-gray-900 dark:text-white">Total:</span>
          <span class="font-semibold text-gray-900 dark:text-white">{{
            formatCurrency(amount + fees)
          }}</span>
        </div>

        <div
          v-if="processing && timeRemaining > 0"
          class="border-t border-gray-200 dark:border-zinc-700 pt-2 flex justify-between text-sm"
        >
          <span class="text-gray-600 dark:text-gray-400">Time remaining:</span>
          <span class="font-medium text-gray-900 dark:text-white">
            {{ formatTime(timeRemaining) }}
          </span>
        </div>
      </div>

      <div class="flex space-x-3">
        <button
          @click="$emit('cancel')"
          type="button"
          class="flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="processPayment"
          :disabled="!canProceed || processing"
          type="button"
          class="flex-1 px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="processing" class="flex items-center justify-center">
            <CoreLoader :width="8" :height="8" color="white" class="mr-2"/>
            <span v-if="timeRemaining > 0">
              Processing... ({{ formatTime(timeRemaining) }})
            </span>
            <span v-else>Processing...</span>
          </span>
          <span v-else>
            <span v-if="selectedPaymentMethod === 'mobile_money'">Pay with Mobile Money</span>
            <span v-else-if="selectedPaymentMethod === 'card'">Pay with Card</span>
            <span v-else-if="selectedPaymentMethod === 'bank_account'">Pay with Bank Account</span>
            <span v-else>Pay</span>
            {{ formatCurrency(amount + fees) }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted, onMounted } from "vue";
import { ENDPOINTS } from "@/utils/api";
import { useHttpClient } from "@/composables/useHttpClient";

interface MobileMoneyOperator {
  id: string;
  ref_id: string;
  name: string;
  country: string;
  currency: string;
}

interface Props {
  amount: number;
  fees?: number;
  type: "tickets" | "booking";
  metadata?: any;
  timeout?: number;
}

const props = withDefaults(defineProps<Props>(), {
  fees: 0,
  timeout: 50000,
});

const emit = defineEmits<{
  cancel: [];
  success: [data: any];
  error: [error: string];
  timeout: [data: any];
}>();

const httpClient = useHttpClient();
const mobileMoneyOperators = ref<MobileMoneyOperator[]>([]);
const banks = ref<any[]>([]);
const loadingOperators = ref<boolean>(false);
const loadingBanks = ref<boolean>(false);
const operatorsError = ref<string>("");
const banksError = ref<string>("");

const selectedPaymentMethod = ref<'mobile_money' | 'card' | 'bank_account'>('mobile_money');
const selectedOperator = ref<MobileMoneyOperator | null>(null);
const selectedBank = ref<any>(null);
const phoneNumber = ref<string>("");
const phoneError = ref<string>("");

// Card details
const cardDetails = ref({
  number: '',
  expiry_month: '',
  expiry_year: '',
  cvv: ''
});
const cardErrors = ref<any>({});

const bankAccountDetails = ref({
  bank_uuid: '',
  account_number: '',
  account_name: '',
  bank_name: ''
});
const bankAccountErrors = ref<any>({});

const processing = ref<boolean>(false);
const timeoutId = ref<NodeJS.Timeout | null>(null);
const timeRemaining = ref<number>(0);
const showTimeoutWarning = ref<boolean>(false);

const fetchOperators = async (): Promise<void> => {
  loadingOperators.value = true;
  operatorsError.value = "";

  try {
    const response = (await httpClient.get(
      ENDPOINTS.PAYMENTS.MOBILE_MONEY_OPERATORS
    )) as { data: MobileMoneyOperator[] };
    mobileMoneyOperators.value = response.data || [];
  } catch (error: any) {
    operatorsError.value =
      error.data?.message || error.message || "Failed to load payment methods";
    console.error("Failed to fetch mobile money operators:", error);
  } finally {
    loadingOperators.value = false;
  }
};

const fetchBanks = async (): Promise<void> => {
  loadingBanks.value = true;
  banksError.value = "";

  try {
    const response = (await httpClient.get(
      ENDPOINTS.PAYMENTS.BANKS
    )) as { data: any[] };
    banks.value = response.data || [];
  } catch (error: any) {
    banksError.value =
      error.data?.message || error.message || "Failed to load banks";
    console.error("Failed to fetch banks:", error);
    // Don't block the component if banks fail to load
    banks.value = [];
  } finally {
    loadingBanks.value = false;
  }
};

const getOperatorLogo = (operatorName: string): string | undefined => {
  const name = operatorName.toLowerCase();
  if (name.includes("airtel")) {
    return "/assets/images/airtel.png";
  } else if (name.includes("mpamba") || name.includes("tnm")) {
    return "/assets/images/tnm.jpg";
  }
  return undefined;
};

const canProceed = computed(() => {
  if (selectedPaymentMethod.value === 'mobile_money') {
    if (!selectedOperator.value) return false;
    return phoneNumber.value.length >= 9 && !phoneError.value;
  } else if (selectedPaymentMethod.value === 'card') {
    return cardDetails.value.number &&
           cardDetails.value.expiry_month &&
           cardDetails.value.expiry_year &&
           cardDetails.value.cvv &&
           Object.keys(cardErrors.value).length === 0;
  } else if (selectedPaymentMethod.value === 'bank_account') {
    return selectedBank.value &&
           bankAccountDetails.value.account_number &&
           bankAccountDetails.value.account_name &&
           Object.keys(bankAccountErrors.value).length === 0;
  }
  return false;
});

watch(phoneNumber, (newValue) => {
  phoneError.value = "";

  if (newValue && !/^\d{9}$/.test(newValue)) {
    phoneError.value = "Please enter a valid 9-digit phone number";
  }
});

watch(cardDetails, (newValue) => {
  cardErrors.value = {};

  if (newValue.number && !/^\d{13,19}$/.test(newValue.number.replace(/\s/g, ''))) {
    cardErrors.value.number = "Please enter a valid card number";
  }

  if (newValue.expiry_month && (!/^\d{2}$/.test(newValue.expiry_month) || parseInt(newValue.expiry_month) < 1 || parseInt(newValue.expiry_month) > 12)) {
    cardErrors.value.expiry_month = "Please enter a valid month (01-12)";
  }

  if (newValue.expiry_year && (!/^\d{4}$/.test(newValue.expiry_year) || parseInt(newValue.expiry_year) < new Date().getFullYear())) {
    cardErrors.value.expiry_year = "Please enter a valid future year";
  }

  if (newValue.cvv && !/^\d{3,4}$/.test(newValue.cvv)) {
    cardErrors.value.cvv = "Please enter a valid CVV";
  }
}, { deep: true });

watch(bankAccountDetails, (newValue) => {
  bankAccountErrors.value = {};

  if (newValue.account_number && !/^\d+$/.test(newValue.account_number)) {
    bankAccountErrors.value.account_number = "Please enter a valid account number";
  }

  if (newValue.account_name && newValue.account_name.length < 2) {
    bankAccountErrors.value.account_name = "Please enter a valid account name";
  }
}, { deep: true });

watch(selectedBank, (newBank) => {
  if (newBank) {
    bankAccountDetails.value.bank_uuid = newBank.uuid;
    bankAccountDetails.value.bank_name = newBank.name;
  }
});

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-MW", {
    style: "currency",
    currency: "MWK",
    minimumFractionDigits: 0,
  }).format(amount);
};

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const startTimeout = () => {
  timeRemaining.value = props.timeout;
  showTimeoutWarning.value = false;

  const updateTimer = () => {
    if (timeRemaining.value <= 0) {
      handleTimeout();
      return;
    }

    if (timeRemaining.value <= 60 && !showTimeoutWarning.value) {
      showTimeoutWarning.value = true;
    }

    timeRemaining.value--;
    timeoutId.value = setTimeout(updateTimer, 1000);
  };

  timeoutId.value = setTimeout(updateTimer, 1000);
};

const clearPaymentTimeout = (): void => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
    timeoutId.value = null;
  }
  timeRemaining.value = 0;
  showTimeoutWarning.value = false;
};

const handleTimeout = (): void => {
  clearPaymentTimeout();
  processing.value = false;

  const paymentData = {
    operator_id: selectedOperator.value?.ref_id || "",
    operator_name: selectedOperator.value?.name || "",
    amount: props.amount + props.fees,
    type: props.type,
    metadata: props.metadata,
    phone_number: phoneNumber.value,
  };

  emit("timeout", paymentData);
  emit("error", "Payment timed out. Please try again.");
};

const processPayment = async () => {
  if (!canProceed.value) return;

  processing.value = true;
  startTimeout();

  try {
    let response: any;

    if (selectedPaymentMethod.value === 'mobile_money') {
      if (!selectedOperator.value) return;

      const paymentData = {
        operator_id: selectedOperator.value.ref_id,
        phone_number: phoneNumber.value,
        amount: props.amount + props.fees,
        type: props.type,
        metadata: props.metadata,
      };

      if (props.type === 'tickets') {
        response = await httpClient.post(ENDPOINTS.PAYMENTS.TICKETS, {
          tickets: props.metadata?.tickets || [],
          phone_number: phoneNumber.value,
          operator_id: selectedOperator.value.ref_id,
        });
      } else if (props.type === 'booking') {
        response = await httpClient.post(ENDPOINTS.PAYMENTS.INITIALIZE, {
          ...paymentData,
          booking_id: props.metadata?.booking_id,
        });
      } else {
        response = await httpClient.post(ENDPOINTS.PAYMENTS.INITIALIZE, paymentData);
      }

      const successData = {
        reference: response.data.reference,
        charge_id: response.data.charge_id,
        amount: props.amount + props.fees,
        operator_name: selectedOperator.value.name,
        phone_number: phoneNumber.value,
        type: props.type,
        metadata: props.metadata,
      };

      emit("success", successData);

    } else if (selectedPaymentMethod.value === 'card') {
      if (props.type === 'tickets') {
        response = await httpClient.post(ENDPOINTS.PAYMENTS.TICKETS_CARD, {
          tickets: props.metadata?.tickets || [],
          card: cardDetails.value,
        });
      } else {
        throw new Error('Card payments for this type are not yet supported');
      }

      const successData = {
        reference: response.data.reference,
        charge_id: response.data.charge_id,
        amount: props.amount + props.fees,
        payment_method: 'card',
        card_last_four: cardDetails.value.number.slice(-4),
        type: props.type,
        metadata: props.metadata,
      };

      emit("success", successData);

    } else if (selectedPaymentMethod.value === 'bank_account') {
      if (props.type === 'tickets') {
        response = await httpClient.post(ENDPOINTS.PAYMENTS.TICKETS_BANK_ACCOUNT, {
          tickets: props.metadata?.tickets || [],
          bank_account: bankAccountDetails.value,
        });
      } else {
        throw new Error('Bank account payments for this type are not yet supported');
      }

      const successData = {
        reference: response.data.reference,
        charge_id: response.data.charge_id,
        amount: props.amount + props.fees,
        payment_method: 'bank_account',
        bank_name: bankAccountDetails.value.bank_name,
        account_last_four: bankAccountDetails.value.account_number.slice(-4),
        type: props.type,
        metadata: props.metadata,
      };

      emit("success", successData);
    }

  } catch (error: any) {
    clearPaymentTimeout();
    processing.value = false;
    const errorMessage = error.response?.data?.message || error.message || "Payment failed";
    emit("error", errorMessage);
  }
};

const stopTimeout = () => {
  clearPaymentTimeout();
  processing.value = false;
};

onMounted(() => {
  fetchOperators();
  // Fetch banks but don't block if it fails
  fetchBanks().catch(() => {
    // Banks are optional, mobile money should still work
    console.log('Banks not available, continuing with mobile money only');
  });
});

defineExpose({
  stopTimeout,
});

onUnmounted(() => {
  clearPaymentTimeout();
});
</script>
