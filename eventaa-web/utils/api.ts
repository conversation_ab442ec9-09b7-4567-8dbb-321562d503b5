export const ENDPOINTS = {
    AUTH: {
        LOGIN: '/auth/login',
        GOOGLE: '/auth/google/web-login',
        REGISTER: '/auth/register',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        RESET_PASSWORD: '/auth/reset-password-a',
        RESEND_VERIFICATION: '/email/verify/resend',
        DEACTIVATE_USER: '/user/deactivate',
    },
    USERS: {
        BASE: '/user',
        GET: '/users',
        SHOW: '/users',
        PROFILE: '/users/profile',
        STATS: '/users/stats',
        UPDATE_STATUS: '/users/update-status',
        DELETE: '/users/delete',
        SUSPEND: '/users/suspend',
        ACTIVATE: '/users/activate',
    },
    ROLES: {
        BASE: '/roles',
        GET: '/roles',
        SHOW: '/roles',
        USERS: '/roles',
        STORE: '/roles',
        UPDATE: '/roles',
        DESTROY: '/roles',
        ASSIGN_PERMISSIONS: '/roles',
    },
    PERMISSIONS: {
        BASE: '/permissions',
        GET: '/permissions',
        CATEGORIES: '/permissions/categories',
        SHOW: '/permissions',
        ROLES: '/permissions',
        STORE: '/permissions',
        UPDATE: '/permissions',
        DESTROY: '/permissions',
        ASSIGN_TO_ROLES: '/permissions',
    },
    NOTIFICATIONS: {
        BASE: '/notifications',
        GET: '/notifications/get',
        UNREAD_COUNT: '/notifications/unread-count',
        READ: '/notifications/mark-as-read',
        READ_ALL: '/notifications/mark-all-as-read',
        DELETE: '/notifications/delete',
    },
    PROFILE: {
        USER: '/profile/user',
        BIO: '/profile/update-bio',
        UPDATE: '/profile/update',
        UPDATE_PHOTO: 'profile/change-photo'
    },
    EVENTS: {
        BASE: '/events',
        GET: '/events/get',
        READ: '/events/read',
        SLUG: '/events/slug',
        CREATE: '/events/create',
        UPDATE: '/events/update',
        DELETE: '/events/delete',
        GET_ALL: '/events/get-all',
        ATTENDANCE: '/events/add-attendance',
        LIKE: '/events/like',
        SPONSOR: '/events/add-sponsors',
        PUBLISH: '/events/publish',
        USER: '/events/user',
        SEARCH: '/events/search',
        TRENDING: '/events/trending',
        RECOMMENDED: '/events/recommended',
        NEARBY: '/events/nearby',
        TICKETS: '/events/tickets',
        CALENDAR: '/events/calendar',
        INVITATIONS: {
            SEND: '/events/invitations/send',
            LIST: '/events/invitations',
            DELETE: '/events/invitations/delete',
            RESEND: '/events/invitations/resend',
        },
    },
    RATING: {
        BASE: '/rating',
        CREATE: '/rating/create',
        READ: '/rating/read',
        UPDATE: '/rating/update',
        DELETE: '/rating/delete',
        GET_ALL: '/rating/get-all',
    },
    CATEGORIES: {
        BASE: '/category/index',
        GET_ALL: '/categories/get-all',
    },
    TIERS: {
        BASE: '/tiers',
        GET_ALL: '/tiers/get-all',
        CREATE: '/tiers/create',
        READ: '/tiers/read',
    },
    TICKETS: {
        GENERATE: '/tickets/generate',
        PURCHASE: '/tickets/purchase',
        CONFIRM_PURCHASE: '/tickets/confirm-purchase',
        MY_PURCHASES: '/tickets/my-purchases',
        DOWNLOAD: '/tickets/download',
        REQUEST_REFUND: '/tickets/request-refund',
        CHECK_REFUND_ELIGIBILITY: '/tickets/refund-eligibility',
        SCAN: '/tickets/scan',
    },
    SPONSORS: {
        BASE: '/sponsors',
        GET_ALL: '/sponsors/get-all',
        CREATE: '/sponsors/create',
        UPDATE: '/sponsors/update',
        READ: '/sponsors/read',
        DELETE: '/sponsors/delete',
    },
    METADATA: {
        LOCATION: '/metadata/location',
    },
    GOOGLE: {
        GEOCODE: 'https://maps.googleapis.com/maps/api/geocode/json?latlng'
    },
    INTERESTS: {
        BASE: '/interest',
        GET_ALL: '/interest/get-all',
        CREATE: '/interest/create',
        READ: '/interest/read',
        UPDATE: '/interest/update',
        DELETE: '/interest/delete',
    },
    BLOG: {
        TWITTER: '/blog/tweets?hashtag=malawi',
        CREATE: '/blog/articles/create',
        READ: '/blog/articles',
        DELETE: '/blog/articles/delete',
    },

    VENUES: {
        BASE: '/venues',
        GET_ALL: '/venues/get',
        USER: '/venues/user',
        CREATE: '/venues/create',
        READ: '/venues/read',
        UPDATE: '/venues/update',
        DELETE: '/venues/delete',
        RATE: '/venues/rate',
        BOOKINGS: 'venues/bookings',
        LOCATIONS: '/venues/locations',
        SEARCH: '/venues/search',
    },
    CURRENCIES: {
        BASE: '/currency',
        GET_ALL: '/currencies',
        ALL: '/currencies/all',
        CREATE: '/currencies',
        READ: '/currencies',
        UPDATE: '/currencies',
        DELETE: '/currencies',
        STATS: '/currencies/stats'
    },
    NEWSLETTER: {
        SUBSCRIBE: '/newsletter/subscribe',
        UNSUBSCRIBE: '/newsletter/unsubscribe',
        PREFERENCES: '/newsletter/preferences',
        UPDATE_PREFERENCES: '/newsletter/preferences',
        REACTIVATE: '/newsletter/reactivate',
        SEND: '/newsletter/send',
        STATS: '/newsletter/stats'
    },
    FOLLOWERS: {
        CREATE: '/followers/create',
        UNFOLLOW: '/followers/unfollow',
        STATUS: '/followers/status',
        READ: '/followers/read',
        DELETE: '/followers/delete',
    },
    BOOKINGS: {
        BASE: '/venue-bookings',
        GET_ALL: '/venue-bookings/get-all',
        CREATE: '/venue-bookings/create',
        READ: '/venue-bookings/read',
        UPDATE: '/venue-bookings/update',
        DELETE: '/venue-bookings/delete',
        CHANGE_STATUS: '/venue-bookings/update-status'
    },
    VENDORS: {
        BASE: '/vendors',
        GET_ALL: '/vendors/get',
        CREATE: '/vendors/create',
        READ: '/vendors/read',
        UPDATE: '/vendors/update',
        DELETE: '/vendors/delete',
        PENDING: '/vendors/pending',
        APPROVE: '/vendors/approve',
        REJECT: '/vendors/reject',
        SUSPEND: '/vendors/suspend',
        REACTIVATE: '/vendors/reactivate',
        LIKE: '/vendors/like',
        LIKE_STATUS: '/vendors/like-status',
        FILTER_OPTIONS: '/vendors/filter-options',
        STATS: '/vendors/stats',
        CATEGORIES: '/vendors/categories',
        LOCATIONS: '/vendors/locations',
        EXPORT: '/vendors/export',
        ANALYTICS: '/vendors/analytics/dashboard',
        SHARE: '/vendors/share',
        SHARE_STATS: '/vendors/share/stats',
        SEARCH: '/vendors/search',
        SERVICES: {
            GET: '/vendors/services/get',
            CREATE: '/vendors/services/create',
            READ: '/vendors/services/read',
            UPDATE: '/vendors/services/update',
            DELETE: '/vendors/services/delete',
        },
        PRICES: {
            GET: '/vendors/prices',
            CREATE: '/vendors/prices/create',
            READ: '/vendors/prices/read',
            UPDATE: '/vendors/prices/update',
            DELETE: '/vendors/prices/delete',
        },
        CUSTOM_PRICING: {
            GET: '/vendors/custom-pricing',
            CREATE: '/vendors/custom-pricing/create',
            UPDATE: '/vendors/custom-pricing/update',
        },
        PORTFOLIO: {
            GET: '/vendors/portfolio',
            CREATE: '/vendors/portfolio/create',
            READ: '/vendors/portfolio/read',
            UPDATE: '/vendors/portfolio/update',
            DELETE: '/vendors/portfolio/delete',
        },
        RATINGS: {
            GET: '/vendors/ratings/get',
            CREATE: '/vendors/ratings/create',
            READ: '/vendors/ratings/read',
            UPDATE: '/vendors/ratings/update',
            DELETE: '/vendors/ratings/delete',
            USER: '/vendors/ratings/user',
            PENDING_COUNT: '/vendors/ratings/pending-count',
            CAN_RATE: '/vendors/ratings/can-rate',
        },
    },
    SERVICES: {
        BASE: '/services/get',
        GET: '/services/get',
        CREATE: '/services/create',
        READ: '/services/read',
        UPDATE: '/services/update',
        DELETE: '/services/delete',
    },
    SETTINGS: {
        BASE: '/settings',
        GET: '/settings/get',
        CREATE: '/settings/create',
        DELETE: '/settings/delete',
        TOGGLE: '/settings/toggle',
        SESSIONS: '/settings/sessions',
        REVOKE_SESSION: '/settings/revoke-session',
    },
    TWO_FACTOR: {
        BASE: '/2fa',
        STATUS: '/2fa/status',
        ENABLE: '/2fa/enable',
        DISABLE: '/2fa/disable',
        VERIFY: '/2fa/verify',
        SEND_CODE: '/2fa/send-code',
    },
    PAYMENTS: {
        BASE: '/payments',
        INITIALIZE: '/payments/initialize',
        VERIFY: '/payments/verify',
        METHODS: '/payments/methods',
        USER_TRANSACTIONS: '/payments/user-transactions',
        VENDOR_TRANSACTIONS: '/payments/vendor-transactions',
        USER_RECEIVED_TRANSACTIONS: '/payments/user-received-transactions',
        WEBHOOK: '/payments/webhook',
        INVOICES: '/payments/invoices',
        STATS: '/payments/stats',
        REFUND: '/payments/refund',
        RECEIPT: '/payments/receipt',
        TICKETS: '/payments/tickets',
        TICKETS_CARD: '/payments/tickets/card',
        TICKETS_BANK_ACCOUNT: '/payments/tickets/bank-account',
        BANKS: '/payments/banks',

        MOBILE_MONEY_OPERATORS: '/payments/mobile-money-operators'
    },
    MESSAGES: {
        BASE: '/messages',
        GET_CONVERSATIONS: '/messages/conversations',
        GET_OR_CREATE_CONVERSATION: '/messages/conversations',
        GET_MESSAGES: '/messages/conversations',
        SEND: '/messages/send',
        MARK_AS_READ: '/messages/read',
        GET_USER_STATUS: '/messages/status'
    },
    DASHBOARD: {
        ADMIN: {
            STATS: '/dashboard/admin/stats',
            ACTIVITIES: '/dashboard/admin/activities',
            REVENUE_CHART: '/dashboard/admin/revenue-chart',
            USER_GROWTH_CHART: '/dashboard/admin/user-growth-chart'
        },
        HOST: {
            STATS: '/dashboard/host/stats',
            ACTIVITIES: '/dashboard/host/activities',
            REVENUE_CHART: '/dashboard/host/revenue-chart',
            TICKET_SALES_CHART: '/dashboard/host/ticket-sales-chart'
        }
    },
    LANDING: {
        STATS: '/landing/stats'
    },
    ANALYTICS: {
        STATS: '/analytics/stats',
        EVENTS: '/analytics/events',
        REVENUE: '/analytics/revenue',
        ATTENDEES: '/analytics/attendees'
    },
    REPORTS: {
        BASE: '/reports',
        GENERATE: '/reports/generate',
        EXPORT: '/reports/export',
        STATS: '/reports/stats'
    },
    ATTENDEES: {
        BASE: '/attendees',
        GET: '/attendees/get',
        CREATE: '/attendees/create',
        UPDATE: '/attendees/update',
        DELETE: '/attendees/delete',
        CHECKIN: '/attendees/checkin',
        EXPORT: '/attendees/export',
        STATS: '/attendees/stats',
        TICKET_TYPES: '/attendees/ticket-types'
    },
    CHECKINS: {
        BASE: '/check-ins',
        GET: '/attendees/get',
        CHECKIN: '/attendees/checkin',
        UNDO: '/attendees/checkin',
        SCAN: '/attendees/checkin/scan',
        STATS: '/attendees/stats',
        EXPORT: '/attendees/export'
    },
    CALENDAR: {
        BASE: '/calendar',
        EVENTS: '/calendar/events',
        AVAILABILITY: '/calendar/availability'
    },
    EMAIL_CAMPAIGNS: {
        BASE: '/email-campaigns',
        GET: '/email-campaigns/get',
        CREATE: '/email-campaigns/create',
        UPDATE: '/email-campaigns/update',
        DELETE: '/email-campaigns/delete',
        SEND: '/email-campaigns/send',
        STATS: '/email-campaigns/stats',
        TEMPLATES: '/email-campaigns/templates'
    },
    INVOICES: {
        BASE: '/invoices',
        GET: '/invoices/get',
        CREATE: '/invoices/create',
        UPDATE: '/invoices/update',
        DELETE: '/invoices/delete',
        SEND: '/invoices/send',
        DOWNLOAD: '/invoices/download',
        STATS: '/invoices/stats'
    },
    REVIEWS: {
        BASE: '/reviews',
        GET: '/reviews/get',
        CREATE: '/reviews/create',
        UPDATE: '/reviews/update',
        DELETE: '/reviews/delete',
        APPROVE: '/reviews/approve',
        REJECT: '/reviews/reject',
        STATS: '/reviews/stats',
        EXPORT: '/reviews/export'
    },
    SOCIAL_MEDIA: {
        BASE: '/social-media',
        ACCOUNTS: '/social-media/accounts',
        CONNECT: '/social-media/connect',
        DISCONNECT: '/social-media/disconnect',
        POST: '/social-media/post',
        SCHEDULE: '/social-media/schedule',
        STATS: '/social-media/stats'
    },
    VENDOR_APPROVAL: {
        BASE: '/vendor-approval',
        PENDING: '/vendor-approval/pending',
        APPROVE: '/vendor-approval/approve',
        REJECT: '/vendor-approval/reject',
        STATS: '/vendor-approval/stats'
    },
    VENDOR_BOOKINGS: {
        BASE: '/vendor-bookings',
        GET: '/vendor-bookings/get',
        CREATE: '/vendor-bookings/create',
        UPDATE: '/vendor-bookings/update',
        DELETE: '/vendor-bookings/delete',
        APPROVE: '/vendor-bookings/approve',
        REJECT: '/vendor-bookings/reject',
        STATS: '/vendor-bookings/stats'
    },

    REFUNDS: {
        BASE: '/host-refunds',
        GET: '/host-refunds',
        SHOW: '/host-refunds',
        APPROVE: '/host-refunds',
        REJECT: '/host-refunds',
        STATISTICS: '/host-refunds/statistics',
        EXPORT: '/host-refunds/export'
    },
    HOST_REQUESTS: {
        BASE: '/host-requests',
        STORE: '/host-requests',
        MY_REQUEST: '/host-requests/my-request',
        INDEX: '/host-requests',
        STATISTICS: '/host-requests/statistics',
        SHOW: '/host-requests',
        APPROVE: '/host-requests',
        REJECT: '/host-requests',
        EXPORT: '/host-requests/export'
    },
    WITHDRAWALS: {
        BASE: '/withdrawals',
        GET: '/withdrawals',
        CREATE: '/withdrawals',
        SHOW: '/withdrawals',
        CANCEL: '/withdrawals',
        BALANCE: '/withdrawals/balance',
        HISTORY: '/withdrawals/history',
        SETTINGS: '/withdrawals/settings',
        EXPORT: '/withdrawals/export'
    }
} as const;
