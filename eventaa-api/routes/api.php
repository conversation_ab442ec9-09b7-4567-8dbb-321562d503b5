<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Events\TestEvent;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\FollowerController;

// Health check route for deployment
Route::get('/health', function () {
    try {
        // Check database connection
        DB::connection()->getPdo();

        // Check cache
        $cacheKey = 'health_check_' . time();
        Cache::put($cacheKey, 'ok', 60);
        $cacheTest = Cache::get($cacheKey);

        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'database' => 'connected',
            'cache' => $cacheTest === 'ok' ? 'working' : 'error',
            'app' => [
                'name' => config('app.name'),
                'env' => config('app.env'),
                'version' => '1.0.0'
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'timestamp' => now()->toISOString(),
            'error' => $e->getMessage()
        ], 500);
    }
});

// Test route for ticket filtering
Route::get('/test-ticket-filters', function () {
    $event = \App\Models\Event::first();

    if (!$event) {
        return response()->json(['error' => 'No events found']);
    }

    $allPurchases = $event->ticketPurchases()->count();
    $scannedPurchases = $event->ticketPurchases()->scanned()->count();
    $unscannedPurchases = $event->ticketPurchases()->unscanned()->count();
    $boughtPurchases = $event->ticketPurchases()->bought()->count();
    $notBoughtPurchases = $event->ticketPurchases()->notBought()->count();

    return response()->json([
        'event_id' => $event->id,
        'event_title' => $event->title,
        'total_purchases' => $allPurchases,
        'scanned_purchases' => $scannedPurchases,
        'unscanned_purchases' => $unscannedPurchases,
        'bought_purchases' => $boughtPurchases,
        'not_bought_purchases' => $notBoughtPurchases,
        'scope_methods_working' => true
    ]);
});
use App\Http\Controllers\InterestController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\MetadataController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\RefundController;
use App\Http\Controllers\RetailerController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SponsorController;

use App\Http\Controllers\MessageController;
use App\Http\Controllers\HostRequestController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\TierController;
use App\Http\Controllers\TwitterController;
use App\Http\Controllers\TwoFactorAuthController;
use App\Http\Controllers\VendorAnalyticsController;
use App\Http\Controllers\VendorBookingController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\VendorRatingController;
use App\Http\Controllers\VendorServicesController;
use App\Http\Controllers\VenuesController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\VerifyEmailController;
use App\Http\Controllers\WithdrawalController;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DashboardController;

Route::get('/test-websockets', function () {
    event(new TestEvent('Hello WebSockets!'));
    return response()->json(['message' => 'Test event sent']);
});

// Test routes for debugging WebSocket events
Route::middleware(['auth:sanctum'])->prefix('test')->group(function () {
    Route::post('/ticket-purchase-event', function (Request $request) {
        $user = $request->user();

        // Get some recent ticket purchases for this user or create mock data
        $purchases = \App\Models\TicketPurchase::where('user_id', $user->id)
            ->latest()
            ->take(1)
            ->get();

        if ($purchases->isEmpty()) {
            // Create mock purchase data
            $mockPurchases = collect([
                (object) [
                    'id' => 999,
                    'purchase_reference' => 'TEST-' . uniqid(),
                    'event_id' => 1,
                    'ticket_id' => 1,
                    'quantity' => 2,
                    'total_amount' => 50.00,
                    'status' => 'completed',
                ]
            ]);

            $purchases = $mockPurchases;
        }

        try {
            // Fire the event
            event(new \App\Events\TicketPurchaseCompleted(
                $purchases,
                $user,
                (float) $purchases->sum('total_amount')
            ));

            return response()->json([
                'success' => true,
                'message' => 'Test event broadcasted successfully',
                'user_id' => $user->id,
                'purchase_count' => count($purchases)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to broadcast event: ' . $e->getMessage()
            ], 500);
        }
    });
});

Route::middleware(['auth:sanctum', 'token.ability:access', 'reset.deactivation'])->prefix('profile')->group(function () {
    Route::get('user', [ProfileController::class, 'getProfile'])->name('profile.user');
    Route::post('/update-bio', [ProfileController::class, 'updateBio'])->name('profile.update-bio');
    Route::post('change-photo', [ProfileController::class, 'changeProfilePhoto'])->name('profile.photo');
    Route::post('update', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('firebase-token', [ProfileController::class, 'saveFirebaseToken'])->name('firebase.token');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
    Route::post('/user/deactivate', [AuthController::class, 'deactivateUser']);
});

Route::get('/currency', function () {
    return response()->json([
        'currencies' => Currency::all()
    ]);
})->middleware('auth:sanctum');

Route::prefix('currencies')->middleware(['auth:sanctum'])->group(function () {
    Route::get('/', [CurrencyController::class, 'index'])->name('currencies.index');
    Route::get('/all', [CurrencyController::class, 'all'])->name('currencies.all');
    Route::get('/stats', [CurrencyController::class, 'stats'])->name('currencies.stats');
    Route::get('/{id}', [CurrencyController::class, 'show'])->name('currencies.show');
    Route::post('/', [CurrencyController::class, 'store'])->name('currencies.store');
    Route::put('/{id}', [CurrencyController::class, 'update'])->name('currencies.update');
    Route::delete('/{id}', [CurrencyController::class, 'destroy'])->name('currencies.destroy');
});

Route::get('/email/verify/{id}/{hash}', [VerifyEmailController::class, '__invoke'])
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.email.verify');

Route::post('/email/verify/resend', [AuthController::class, 'resendVerificationEmail'])
    ->middleware(['throttle:6,1'])
    ->name('verification.send');

Route::group(['prefix' => 'auth'], function () {
    Route::post('google/login', [AuthController::class, 'handleGoogleCallback'])->name('auth.google.login');
    Route::post('google/web-login', [AuthController::class, 'googleLogin'])->name('auth.google.web-login');
    Route::post('login', [AuthController::class, 'login'])->name('auth.login');
    Route::post('register', [AuthController::class, 'register'])->name('auth.register');
    Route::post('forgot-password', [AuthController::class, 'forgotPassword'])->name('password.reset');
    Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('auth.resetPassword');
    Route::post('logout', [AuthController::class, 'logout'])->name('auth.logout');
    Route::post('refresh', [AuthController::class, 'refresh'])->middleware(['auth:sanctum', 'token.ability:refresh'])->name('auth.refresh');
});

Route::middleware('auth:sanctum')->post('/auth/reset-password-a', [AuthController::class, 'authResetPassword'])->name('auth.authResetPassword');

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('settings')->group(function () {
    Route::get('get', [SettingController::class, 'getUserSettings'])->name('settings.get');
    Route::post('create', [SettingController::class, 'addUserSetting'])->name('settings.create');
    Route::post('delete', [SettingController::class, 'deleteUserSettings'])->name('settings.delete');
    Route::post('toggle', [SettingController::class, 'toggleUserSetting'])->name('settings.toggle');
    Route::get('sessions', [SettingController::class, 'getSessions'])->name('settings.sessions');
    Route::post('revoke-session', [SettingController::class, 'revokeSession'])->name('settings.revoke-session');
});

Route::prefix('2fa')->group(function () {
    Route::post('verify', [TwoFactorAuthController::class, 'verifyCode'])->name('2fa.verify');
    Route::post('send-code', [TwoFactorAuthController::class, 'sendCode'])->name('2fa.send-code');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('status', [TwoFactorAuthController::class, 'status'])->name('2fa.status');
        Route::post('enable', [TwoFactorAuthController::class, 'enable'])->name('2fa.enable');
        Route::post('disable', [TwoFactorAuthController::class, 'disable'])->name('2fa.disable');
    });
});

Route::prefix('payments')->group(function () {
    Route::post('webhook', action: [PaymentController::class, 'handleWebhook'])->name('payments.webhook');
    Route::get('return', [PaymentController::class, 'handlePaymentReturn'])->name('payments.return');
    Route::get('verify', [PaymentController::class, 'verifyPayment'])->name('payments.verify');
    Route::get('mobile-money-operators', [PaymentController::class, 'getMobileMoneyOperators'])->name('payments.mobile-money-operators');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::post('initialize', [PaymentController::class, 'initializeBookingPayment'])->name('payments.initialize');

        Route::post('tickets', [PaymentController::class, 'initializeTicketPurchase'])->name('payments.tickets');
        Route::post('tickets/card', [PaymentController::class, 'initializeTicketPurchaseWithCard'])->name('payments.tickets.card');
        Route::post('tickets/bank-account', [PaymentController::class, 'initializeTicketPurchaseWithBankAccount'])->name('payments.tickets.bank-account');
        Route::get('banks', [PaymentController::class, 'getBanks'])->name('payments.banks');
        Route::get('methods/{vendor_id}', [PaymentController::class, 'getVendorPaymentMethods'])->name('payments.methods');
        Route::get('user-transactions', [PaymentController::class, 'getUserTransactions'])->name('payments.user-transactions');
        Route::get('user-received-transactions', [PaymentController::class, 'getUserReceivedTransactions'])->name('payments.user-received-transactions');
        Route::get('user-received-transactions/export', [PaymentController::class, 'exportUserReceivedTransactions'])->name('payments.user-received-transactions.export');
        Route::get('vendor-transactions', [PaymentController::class, 'getVendorTransactions'])->name('payments.vendor-transactions');
        Route::post('verify-direct-charge', [PaymentController::class, 'verifyDirectChargeStatus'])->name('payments.verify-direct-charge');
    });
});

Route::group(['prefix' => 'interest'], function () {
    Route::middleware('auth:sanctum')->post('create', [InterestController::class, 'create'])->name('interest.create');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('notifications')->group(function () {
    Route::get('get', [NotificationController::class, 'index'])->name('notifications.get');
    Route::get('unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::put('mark-as-read/{id}', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::get('mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('delete/{id}', [NotificationController::class, 'deleteNotification'])->name('notifications.delete');
});


Route::prefix('events')->group(function () {
    Route::get('get', [EventController::class, 'index'])->name('event.get');
    Route::get('get-all', [EventController::class, 'index'])->name('event.get-all');
    Route::get('search', [EventController::class, 'search'])->name('event.search');
    Route::middleware('auth:sanctum')->get('read/{id}', [EventController::class, 'read'])->name('event.read');
    Route::middleware('auth:sanctum')->get('tickets/{id}', [EventController::class, 'eventTickets'])->name('event.tickets');
    Route::get('trending', [EventController::class, 'getTrending'])->name('event.trending');
    Route::get('slug/{slug}', [EventController::class, 'readBySlug'])->name('event.slug');
    Route::get('category/{id}', [EventController::class, 'byCategory'])->name('event.category');
    Route::post('nearby', [EventController::class, 'getNearbyEvents'])->name('event.nearby');
    Route::get('calendar', [EventController::class, 'calendar'])->name('event.calendar.public');
});


Route::prefix('venues')->group(function () {
    Route::get('get', [VenuesController::class, 'index'])->name('venues.get');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->get('user', [VenuesController::class, 'user'])->name('venues.user');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('create', [VenuesController::class, 'create'])->name('venues.create');
    Route::get('read/{slug}', [VenuesController::class, 'show'])->name('venues.read');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('update/{slug}', [VenuesController::class, 'update'])->name('venues.update');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('update-status/{id}', [VenuesController::class, 'updateStatus'])->name('venues.updateStatus');
    Route::get('bookings/{id}', [VenuesController::class, 'bookings'])->name('venues.bookings');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->delete('delete/{id}', [VenuesController::class, 'destroy'])->name('venues.delete');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('rate', [VenuesController::class, 'rate'])->name('venues.rate');
    Route::get('locations', [VenuesController::class, 'locations'])->name('venues.locations');
});

Route::middleware('auth:sanctum')->group(function () {
    Route::get('venue-bookings', [BookingController::class, 'index']);
    Route::post('venue-bookings', [BookingController::class, 'store']);
    Route::get('venue-bookings/{id}', [BookingController::class, 'show']);
    Route::post('venue-bookings/create-or-update', [BookingController::class, 'createOrUpdate']);
    Route::delete('venue-bookings/{id}', [BookingController::class, 'destroy']);
    Route::patch('venue-bookings/{id}/status', [BookingController::class, 'updateStatus']);
});

Route::prefix('vendors')->group(function () {
    Route::get('get', [VendorController::class, 'index'])->name('vendors.get');
    Route::get('read/{slug}', [VendorController::class, 'read'])->name('vendors.read');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('profile', [VendorController::class, 'profile'])->name('vendors.profile');
        Route::post('create', [VendorController::class, 'create'])->name('vendors.create');
        Route::post('update/{id}', [VendorController::class, 'update'])->name('vendors.update');
        Route::delete('delete/{id}', [VendorController::class, 'destroy'])->name('vendors.delete');
        Route::get('pending', [VendorController::class, 'pendingRequests'])->name('vendors.pending');
        Route::post('approve/{id}', [VendorController::class, 'approve'])->name('vendors.approve');
        Route::post('reject/{id}', [VendorController::class, 'reject'])->name('vendors.reject');
        Route::post('suspend/{id}', [VendorController::class, 'suspend'])->name('vendors.suspend');
        Route::post('reactivate/{id}', [VendorController::class, 'reactivate'])->name('vendors.reactivate');
        Route::post('like', [VendorController::class, 'likeVendor'])->name('vendors.like');
        Route::get('like-status/{vendorId}', [VendorController::class, 'checkLikeStatus'])->name('vendors.like.status');
        Route::get('stats', [VendorController::class, 'stats'])->name('vendors.stats');
        Route::get('categories', [VendorController::class, 'categories'])->name('vendors.categories');
        Route::get('locations', [VendorController::class, 'locations'])->name('vendors.locations');
        Route::get('export', [VendorController::class, 'export'])->name('vendors.export');
    });

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->get('analytics/dashboard', [VendorAnalyticsController::class, 'getDashboardAnalytics'])->name('vendors.analytics.dashboard');

    Route::prefix('services')->group(function () {
        Route::get('get/{vendor_id}', [VendorServicesController::class, 'index'])->name('vendor.services.get');
        Route::post('create', [VendorServicesController::class, 'store'])->name('vendor.services.create');
        Route::get('read/{id}', [VendorServicesController::class, 'show'])->name('vendor.services.read');
        Route::put('update/{id}', [VendorServicesController::class, 'update'])->name('vendor.services.update');
        Route::delete('delete/{id}', [VendorServicesController::class, 'destroy'])->name('vendor.services.delete');
    });

    Route::prefix('prices')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorPriceController::class, 'index'])->name('vendor.prices.get');
        Route::post('create', [App\Http\Controllers\VendorPriceController::class, 'store'])->name('vendor.prices.create');
        Route::get('read/{id}', [App\Http\Controllers\VendorPriceController::class, 'show'])->name('vendor.prices.read');
        Route::put('update/{id}', [App\Http\Controllers\VendorPriceController::class, 'update'])->name('vendor.prices.update');
        Route::delete('delete/{id}', [App\Http\Controllers\VendorPriceController::class, 'destroy'])->name('vendor.prices.delete');
    });

    Route::prefix('custom-pricing')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorCustomPricingController::class, 'show'])->name('vendor.custom-pricing.get');
        Route::post('create', [App\Http\Controllers\VendorCustomPricingController::class, 'store'])->name('vendor.custom-pricing.create');
        Route::put('update/{vendor_id}', [App\Http\Controllers\VendorCustomPricingController::class, 'update'])->name('vendor.custom-pricing.update');
    });

    Route::get('filter-options', [VendorController::class, 'getFilterOptions'])->name('vendors.filter.options');

    Route::prefix('bookings')->group(function () {
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::get('get', [VendorBookingController::class, 'index'])->name('vendor.bookings.get');
            Route::get('user', [VendorBookingController::class, 'userBookings'])->name('vendor.bookings.user');
            Route::get('vendor', [VendorBookingController::class, 'vendorBookings'])->name('vendor.bookings.vendor');
            Route::post('create', [VendorBookingController::class, 'store'])->name('vendor.bookings.create');
            Route::get('read/{id}', [VendorBookingController::class, 'show'])->name('vendor.bookings.read');
            Route::put('update/{id}', [VendorBookingController::class, 'update'])->name('vendor.bookings.update');
            Route::put('status/{id}', [VendorBookingController::class, 'updateStatus'])->name('vendor.bookings.status');
            Route::delete('delete/{id}', [VendorBookingController::class, 'destroy'])->name('vendor.bookings.delete');
            Route::get('pending-count', [VendorBookingController::class, 'getPendingCount'])->name('vendor.bookings.pending-count');
        });

        Route::get('available-slots', [VendorBookingController::class, 'getAvailableTimeSlots'])->name('vendor.bookings.available-slots');
    });

    Route::prefix('ratings')->group(function () {
        Route::get('get/{vendor_id}', [VendorRatingController::class, 'index'])->name('vendor.ratings.get');
        Route::post('create', [VendorRatingController::class, 'store'])->name('vendor.ratings.create');
        Route::get('read/{id}', [VendorRatingController::class, 'show'])->name('vendor.ratings.read');
        Route::put('update/{id}', [VendorRatingController::class, 'update'])->name('vendor.ratings.update');
        Route::delete('delete/{id}', [VendorRatingController::class, 'destroy'])->name('vendor.ratings.delete');
        Route::get('user/{vendor_id}', [VendorRatingController::class, 'getUserRating'])->name('vendor.ratings.user');
        Route::get('pending-count', [VendorRatingController::class, 'getPendingCount'])->name('vendor.ratings.pending-count');
        Route::middleware(['auth:sanctum'])->get('can-rate/{vendor_id}', [VendorRatingController::class, 'canRateVendor'])->name('vendor.ratings.can-rate');
    });

    Route::prefix('portfolio')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorPortfolioController::class, 'index'])->name('vendor.portfolio.get');
        Route::post('create', [App\Http\Controllers\VendorPortfolioController::class, 'store'])->name('vendor.portfolio.create');
        Route::get('read/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'show'])->name('vendor.portfolio.read');
        Route::post('update/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'update'])->name('vendor.portfolio.update');
        Route::delete('delete/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'destroy'])->name('vendor.portfolio.delete');
    });

    Route::prefix('share')->group(function () {
        Route::post('/', [App\Http\Controllers\VendorShareController::class, 'share'])->name('vendors.share');
        Route::get('stats/{vendor_id}', [App\Http\Controllers\VendorShareController::class, 'getShareStats'])
            ->middleware(['auth:sanctum', 'reset.deactivation'])
            ->name('vendors.share.stats');
    });
});

Route::middleware(['auth:sanctum'])->prefix('messages')->group(function () {
    Route::get('conversations', [MessageController::class, 'getConversations'])->name('messages.conversations');
    Route::post('conversations', [MessageController::class, 'getOrCreateConversation'])->name('messages.conversations.create');
    Route::get('conversations/{conversationId}', [MessageController::class, 'getMessages'])->name('messages.get');
    Route::post('send', [MessageController::class, 'sendMessage'])->name('messages.send');
    Route::put('read/{conversationId}', [MessageController::class, 'markConversationAsRead'])->name('messages.read');
    Route::delete('{messageId}', [MessageController::class, 'deleteMessage'])->name('messages.delete');
    Route::post('status', [MessageController::class, 'updateOnlineStatus'])->name('messages.status.update');
    Route::get('status/{userId}', [MessageController::class, 'getUserStatus'])->name('messages.status.get');
});

Route::middleware(['auth:sanctum'])->prefix('vendors/messages')->group(function () {
    Route::get('unread-count', [MessageController::class, 'getUnreadCount'])->name('vendors.messages.unread-count');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('events')->group(function () {
    Route::post('create', [EventController::class, 'create'])->name('event.create');
    Route::post('update/{id}', [EventController::class, 'update'])->name('event.update');
    Route::delete('delete/{id}', [EventController::class, 'delete'])->name('event.delete');
    Route::post('add-attendance', [EventController::class, 'addAttendee'])->name('event.addAttendance');
    Route::post('like', [EventController::class, 'likeEvent'])->name('event.like');
    Route::get('user', [EventController::class, 'userEvents'])->name('event.user.events');
    Route::post('share', [EventController::class, 'share'])->name('event.share');
    Route::get('calendar', [EventController::class, 'calendar'])->name('event.calendar');
    Route::post('enable-notifications', [EventController::class, 'allowNotifications'])->name('event.enableNotification');
    Route::post('publish', [EventController::class, 'publish'])->name('event.publish');
    Route::post('add-sponsors', [EventController::class, 'addEventSponsors'])->name('event.addEventSponsors');

    Route::prefix('invitations')->group(function () {
        Route::get('{eventId}', [\App\Http\Controllers\EventInvitationController::class, 'index'])->name('event.invitations.index');
        Route::post('send', [\App\Http\Controllers\EventInvitationController::class, 'send'])->name('event.invitations.send');
        Route::post('resend/{invitationId}', [\App\Http\Controllers\EventInvitationController::class, 'resend'])->name('event.invitations.resend');
        Route::delete('delete/{invitationId}', [\App\Http\Controllers\EventInvitationController::class, 'delete'])->name('event.invitations.delete');
    });
});

Route::prefix('invitations')->group(function () {
    Route::get('{token}', [\App\Http\Controllers\EventInvitationController::class, 'getByToken'])->name('invitations.getByToken');
    Route::post('accept/{token}', [\App\Http\Controllers\EventInvitationController::class, 'accept'])->name('invitations.accept');
    Route::post('decline/{token}', [\App\Http\Controllers\EventInvitationController::class, 'decline'])->name('invitations.decline');
});

Route::group(['prefix' => 'category'], function () {
    Route::get('index', [CategoryController::class, 'index'])->name('category.index');
    Route::post('create', [CategoryController::class, 'create'])->name('category.create');
    Route::get('read/{id}', [CategoryController::class, 'read'])->name('category.read');
    Route::post('update/{id}', [CategoryController::class, 'update'])->name('category.update');
    Route::delete('delete/{id}', [CategoryController::class, 'delete'])->name('category.delete');
});

Route::group(['prefix' => 'rating'], function () {
    Route::get('index', [RatingController::class, 'index'])->name('rating.index');
    Route::middleware('auth:sanctum')->post('create', [RatingController::class, 'create'])->name('rating.create');
    Route::put('read/{id}', [RatingController::class, 'read'])->name('rating.read');
    Route::delete('delete/{id}', [RatingController::class, 'delete'])->name('rating.delete');
});

Route::group(['prefix' => 'followers'], function () {
    Route::middleware('auth:sanctum')->post('create', [FollowerController::class, 'create'])->name('follower.create');
    Route::middleware('auth:sanctum')->post('unfollow', [FollowerController::class, 'unfollow'])->name('follower.unfollow');
    Route::middleware('auth:sanctum')->get('status/{userId}', [FollowerController::class, 'checkFollowStatus'])->name('follower.status');
    Route::get('debug', [FollowerController::class, 'debugFollowers'])->name('follower.debug');
    Route::get('read/{id}', [FollowerController::class, 'read'])->name('follower.read');
    Route::put('read/{id}', [FollowerController::class, 'update'])->name('follower.update');
    Route::middleware('auth:sanctum')->delete('delete/{id}', [FollowerController::class, 'delete'])->name('follower.delete');
});




Route::prefix('payments')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('invoices', [InvoiceController::class, 'index'])->name('payments.invoices');
        Route::get('invoices/{id}', [InvoiceController::class, 'show'])->name('payments.invoice.show');
    });
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('tickets')->group(function () {
    Route::post('generate', [TicketController::class, 'generate'])->name('tickets.generate');
    Route::get('generate-qr-code/{id}', [TicketController::class, 'generateQRCodes'])->name('tickets.generateQRCodes');
    Route::post('scan', [TicketController::class, 'scan'])->name('tickets.scan');
    Route::get('buy', [TicketController::class, 'assignUserTickets'])->name('tickets.buy');

    // New purchase flow routes
    Route::get('event/{eventId}', [TicketController::class, 'getEventTickets'])->name('tickets.event');
    Route::post('purchase', [TicketController::class, 'purchaseTickets'])->name('tickets.purchase');
    Route::post('confirm-purchase', [TicketController::class, 'confirmPurchase'])->name('tickets.confirm-purchase');
    Route::get('my-purchases', [TicketController::class, 'getUserPurchases'])->name('tickets.my-purchases');
    Route::get('download/{purchaseId}', [TicketController::class, 'downloadTicket'])->name('tickets.download');

    // Refund routes
    Route::get('refund-eligibility/{purchaseId}', [TicketController::class, 'checkRefundEligibility'])->name('tickets.refund-eligibility');
    Route::post('request-refund', [TicketController::class, 'requestRefund'])->name('tickets.request-refund');

});

Route::middleware(['auth:sanctum', 'role:admin'])->prefix('refunds')->group(function () {
    Route::get('/', [RefundController::class, 'index'])->name('refunds.index');
    Route::get('/{id}', [RefundController::class, 'show'])->name('refunds.show');
    Route::post('/{id}/approve', [RefundController::class, 'approve'])->name('refunds.approve');
    Route::post('/{id}/reject', [RefundController::class, 'reject'])->name('refunds.reject');
    Route::get('/statistics', [RefundController::class, 'statistics'])->name('refunds.statistics');
    Route::post('/bulk-approve', [RefundController::class, 'bulkApprove'])->name('refunds.bulk-approve');
    Route::get('/export', [RefundController::class, 'export'])->name('refunds.export');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('host-refunds')->group(function () {
    Route::get('/', [RefundController::class, 'hostIndex'])->name('host.refunds.index');
    Route::get('/statistics', [RefundController::class, 'hostStatistics'])->name('host.refunds.statistics');
    Route::get('/export', [RefundController::class, 'hostExport'])->name('host.refunds.export');
    Route::get('/{id}', action: [RefundController::class, 'hostShow'])->name('host.refunds.show');
    Route::post('/{id}/approve', [RefundController::class, 'hostApprove'])->name('host.refunds.approve');
    Route::post('/{id}/reject', [RefundController::class, 'hostReject'])->name('host.refunds.reject');
});

Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin/payment-gateways')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\PaymentGatewayController::class, 'index'])->name('admin.payment-gateways.index');
    Route::get('paychangu', [\App\Http\Controllers\Admin\PaymentGatewayController::class, 'getPayChanguConfig'])->name('admin.payment-gateways.paychangu');
    Route::put('paychangu', [\App\Http\Controllers\Admin\PaymentGatewayController::class, 'updatePayChanguConfig'])->name('admin.payment-gateways.paychangu.update');
    Route::post('paychangu/test', [\App\Http\Controllers\Admin\PaymentGatewayController::class, 'testPayChanguConnection'])->name('admin.payment-gateways.paychangu.test');
    Route::get('statistics', [\App\Http\Controllers\Admin\PaymentGatewayController::class, 'getStatistics'])->name('admin.payment-gateways.statistics');
});

Route::middleware('auth:sanctum')->prefix('tiers')->group(function () {
    Route::get('read/{id}', [TierController::class, 'read'])->name('tickets.read/{id}');
    Route::post('create', [TierController::class, 'create'])->name('tickets.create');
    Route::put('update/{id}', [TierController::class, 'update'])->name('tickets.update');
    Route::delete('delete/{id}', [TierController::class, 'delete'])->name('tickets.delete');
});

Route::middleware('auth:sanctum')->prefix('sponsors')->group(function () {
    Route::get('read', [SponsorController::class, 'read'])->name('sponsors.read/{id}');
    Route::post('create', [SponsorController::class, 'create'])->name('sponsors.create');
    Route::put('update/{id}', [SponsorController::class, 'update'])->name('sponsors.update');
    Route::delete('delete/{id}', [SponsorController::class, 'delete'])->name('sponsors.delete');
});

Route::middleware('auth:sanctum')->prefix('retailers')->group(function () {
    Route::get('read', [RetailerController::class, 'read'])->name('retailers.read/{id}');
    Route::post('create', [RetailerController::class, 'create'])->name('retailers.create');
    Route::put('update/{id}', [RetailerController::class, 'update'])->name('retailers.update');
    Route::delete('delete/{id}', [RetailerController::class, 'delete'])->name('retailers.delete');
});

Route::middleware('auth:sanctum')->prefix('verification')->group(function () {
    Route::get('request', [VerificationController::class, 'request'])->name('verification.request');
    Route::get('verify', [VerificationController::class, 'adminVerify'])->name('verification.verify');
    Route::get('check-status', [VerificationController::class, 'checkStatus'])->name('verification.check-status');
});

Route::middleware(['auth:sanctum'])->prefix('roles')->group(function () {
    Route::get('/', [RoleController::class, 'index'])->name('roles.index');
    Route::get('/{id}', [RoleController::class, 'show'])->name('roles.show');
    Route::get('/{id}/users', [RoleController::class, 'users'])->name('roles.users');
    Route::post('/', [RoleController::class, 'store'])->name('roles.store');
    Route::put('/{id}', [RoleController::class, 'update'])->name('roles.update');
    Route::delete('/{id}', [RoleController::class, 'destroy'])->name('roles.destroy');
    Route::post('/{id}/permissions', [RoleController::class, 'assignPermissions'])->name('roles.permissions.assign');
    Route::get('/{id}/permissions', [RoleController::class, 'getRolePermissions'])->name('roles.permissions.get');

});

Route::middleware(['auth:sanctum'])->prefix('permissions')->group(function () {
    Route::get('/', [PermissionController::class, 'index'])->name('permissions.index');
    Route::get('/categories', [PermissionController::class, 'getByCategory'])->name('permissions.categories');
    Route::get('/{id}', [PermissionController::class, 'show'])->name('permissions.show');
    Route::get('/{id}/roles', [PermissionController::class, 'roles'])->name('permissions.roles');
    Route::post('/', [PermissionController::class, 'store'])->name('permissions.store');
    Route::put('/{id}', [PermissionController::class, 'update'])->name('permissions.update');
    Route::delete('/{id}', [PermissionController::class, 'destroy'])->name('permissions.destroy');
    Route::post('/{id}/roles', [PermissionController::class, 'assignToRoles'])->name('permissions.roles.assign');
});


Route::prefix('metadata')->group(function () {
    Route::post('location', [MetadataController::class, 'getLocationDetails'])->name('verification.location');
});

Route::prefix('blog')->group(function () {
    Route::get('tweets', [TwitterController::class, 'fetchTweetsByHashtag'])->name('blog.twitter');
    Route::get('articles', [ArticleController::class, 'index'])->name('blog.articles');
    Route::get('articles/{id}', [ArticleController::class, 'show'])->name('blog.article');
    Route::middleware('auth:sanctum')->post('articles/create', [ArticleController::class, 'store'])->name('blog.articles.category');
    Route::get('articles/search', [ArticleController::class, 'searchArticles'])->name('blog.articles.search');
    Route::middleware('auth:sanctum')->post('articles/{id}', [ArticleController::class, 'update'])->name('blog.articles.update');
    Route::middleware('auth:sanctum')->delete('articles/delete/{id}', [ArticleController::class, 'destroy'])->name('blog.articles.delete');
});

Route::prefix('newsletter')->group(function () {
    Route::post('subscribe', [NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
    Route::get('unsubscribe/{token}', [NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');
    Route::post('preferences', [NewsletterController::class, 'getPreferences'])->name('newsletter.preferences');
    Route::put('preferences', [NewsletterController::class, 'updatePreferences'])->name('newsletter.preferences.update');
    Route::post('reactivate', [NewsletterController::class, 'reactivate'])->name('newsletter.reactivate');

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('send', [NewsletterController::class, 'sendNewsletter'])->name('newsletter.send');
        Route::get('stats', [NewsletterController::class, 'getStats'])->name('newsletter.stats');
    });
});

Route::middleware('auth:sanctum')->prefix('services')->group(function () {
    Route::get('get', [ServicesController::class, 'index'])->name('services.get');
    Route::post('create', [ServicesController::class, 'store'])->name('services.create');
    Route::get('read/{id}', [ServicesController::class, 'show'])->name('services.read');
    Route::put('update/{id}', [ServicesController::class, 'update'])->name('services.update');
    Route::delete('delete/{id}', [ServicesController::class, 'destroy'])->name('services.delete');
});

Route::prefix('users')->group(function () {
    Route::get('/', [UserController::class, 'index']);
    Route::get('/stats', [UserController::class, 'stats']);
    Route::get('/{id}', [UserController::class, 'show']);
    Route::put('/{id}/status', [UserController::class, 'updateStatus']);
    Route::delete('delete/{id}', [UserController::class, 'destroy']);
});

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('analytics')->group(function () {
        Route::get('/stats', [DashboardController::class, 'getAnalyticsStats']);
        Route::get('/events', [DashboardController::class, 'getTopEvents']);
        Route::get('/revenue', [DashboardController::class, 'getRevenueAnalytics']);
        Route::get('/attendees', [DashboardController::class, 'getAttendeesAnalytics']);
    });

    Route::prefix('reports')->group(function () {
        Route::get('/', [DashboardController::class, 'getReports']);
        Route::get('/stats', [DashboardController::class, 'getReportsStats']);
        Route::post('/generate', [DashboardController::class, 'generateReport']);
        Route::get('/export', [DashboardController::class, 'exportReports']);
        Route::get('/{id}', [DashboardController::class, 'getReport']);
        Route::get('/{id}/download', [DashboardController::class, 'downloadReport']);
        Route::delete('/{id}', [DashboardController::class, 'deleteReport']);
        Route::post('/{id}/regenerate', [DashboardController::class, 'regenerateReport']);
    });

    Route::prefix('payments')->group(function () {
        Route::get('/stats', [PaymentController::class, 'getPaymentStats']);
        Route::post('/refund/{transactionId}', [PaymentController::class, 'processRefund']);
        Route::get('/receipt/{transactionId}', [PaymentController::class, 'downloadReceipt']);
    });

    Route::prefix('withdrawals')->group(function () {
        Route::get('/', [WithdrawalController::class, 'index']);
        Route::post('/', [WithdrawalController::class, 'requestWithdrawal']);
        Route::get('/balance', [WithdrawalController::class, 'getBalance']);
        Route::get('/export', [WithdrawalController::class, 'export']);
        Route::get('/{withdrawal}', [WithdrawalController::class, 'show']);
        Route::patch('/{withdrawal}/cancel', [WithdrawalController::class, 'cancelWithdrawal']);
    });

    // Withdrawal webhook (unprotected)
    Route::post('withdrawals/webhook/paychangu', [WithdrawalController::class, 'handlePayChanguWebhook'])
        ->name('withdrawals.webhook.paychangu');

    Route::prefix('dashboard')->group(function () {
        Route::get('/admin/stats', [DashboardController::class, 'getAdminStats']);
        Route::get('/admin/activities', [DashboardController::class, 'getAdminActivities']);
        Route::get('/admin/revenue-chart', [DashboardController::class, 'getAdminRevenueChart']);
        Route::get('/admin/user-growth-chart', [DashboardController::class, 'getAdminUserGrowthChart']);

        Route::get('/host/stats', [DashboardController::class, 'getHostStats']);
        Route::get('/host/activities', [DashboardController::class, 'getHostActivities']);
        Route::get('/host/revenue-chart', [DashboardController::class, 'getHostRevenueChart']);
        Route::get('/host/ticket-sales-chart', [DashboardController::class, 'getHostTicketSalesChart']);
    });

    Route::prefix('attendees')->group(function () {
        Route::get('/get', [App\Http\Controllers\AttendeeController::class, 'index']);
        Route::post('/create', [App\Http\Controllers\AttendeeController::class, 'create']);
        Route::put('/update/{id}', [App\Http\Controllers\AttendeeController::class, 'update']);
        Route::delete('/delete/{id}', [App\Http\Controllers\AttendeeController::class, 'destroy']);
        Route::post('/checkin/{id}', [App\Http\Controllers\AttendeeController::class, 'checkIn']);
        Route::post('/checkin/{id}/undo', [App\Http\Controllers\AttendeeController::class, 'undoCheckIn']);
        Route::post('/checkin/scan', [App\Http\Controllers\AttendeeController::class, 'scanQR']);
        Route::post('/no-show/{id}', [App\Http\Controllers\AttendeeController::class, 'markNoShow']);
        Route::get('/export', [App\Http\Controllers\AttendeeController::class, 'export']);
        Route::get('/stats', [App\Http\Controllers\AttendeeController::class, 'stats']);
        Route::get('/ticket-types', [App\Http\Controllers\AttendeeController::class, 'ticketTypes']);
    });

    Route::prefix('reviews')->group(function () {
        Route::get('/get', [ReviewController::class, 'index']);
        Route::get('/stats', [ReviewController::class, 'stats']);
        Route::get('/export', [ReviewController::class, 'export']);
        Route::put('/update/{id}', [ReviewController::class, 'update']);
        Route::delete('/delete/{id}', [ReviewController::class, 'destroy']);
    });
});

Route::get('/landing/stats', [DashboardController::class, 'getLandingPageStats']);

// Host Request Routes
Route::prefix('host-requests')->middleware('auth:sanctum')->group(function () {
    // User routes
    Route::post('/', [HostRequestController::class, 'store'])->name('host-requests.store');
    Route::get('/my-request', [HostRequestController::class, 'getUserRequest'])->name('host-requests.my-request');

    // Admin routes
    Route::middleware('role:admin')->group(function () {
        Route::get('/', [HostRequestController::class, 'index'])->name('host-requests.index');
        Route::get('/statistics', [HostRequestController::class, 'statistics'])->name('host-requests.statistics');
        Route::get('/export', [HostRequestController::class, 'export'])->name('host-requests.export');
        Route::get('/{id}', [HostRequestController::class, 'show'])->name('host-requests.show');
        Route::post('/{id}/approve', [HostRequestController::class, 'approve'])->name('host-requests.approve');
        Route::post('/{id}/reject', [HostRequestController::class, 'reject'])->name('host-requests.reject');
    });
});
