<?php

namespace App\Services;

use App\Models\PaymentGateway;
use App\Models\PaymentTransaction;
use App\Models\VendorPaymentMethod;
use App\Models\TicketPurchase;
use App\Models\Ticket;
use App\Models\User;
use App\Events\PaymentStatusUpdated;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PayChanguService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $merchantId;
    protected $testMode;
    protected $webhookSecret;

    public function __construct(PaymentGateway $gateway = null)
    {
        if ($gateway) {
            $this->loadConfig($gateway);
        } else {
            $this->loadDefaultConfig();
        }
    }

    /**
     * Load configuration from a payment gateway model
     */
    public function loadConfig(PaymentGateway $gateway)
    {
        $config = $gateway->config;
        $this->testMode = $gateway->test_mode;

        $this->baseUrl = $this->testMode
            ? 'https://api.paychangu.com'
            : 'https://api.paychangu.com';

        $this->apiKey = $config['api_key'] ?? '';
        $this->secretKey = $config['secret_key'] ?? '';
        $this->merchantId = $config['merchant_id'] ?? '';
        $this->webhookSecret = $config['webhook_secret'] ?? '';
    }

    /**
     * Load configuration from environment variables
     */
    public function loadDefaultConfig()
    {
        $this->testMode = env('PAYCHANGU_TEST_MODE', true);

        $this->baseUrl = $this->testMode
            ? 'https://api.paychangu.com'
            : 'https://api.paychangu.com';

        $this->apiKey = env('PAYCHANGU_API_KEY', '');
        $this->secretKey = env('PAYCHANGU_SECRET_KEY', '');
        $this->merchantId = env('PAYCHANGU_MERCHANT_ID', '');
        $this->webhookSecret = env('PAYCHANGU_WEBHOOK_SECRET', '');

        // Debug logging to check if credentials are loaded
        Log::info('PayChangu config loaded', [
            'api_key' => $this->apiKey ? 'SET' : 'EMPTY',
            'secret_key' => $this->secretKey ? 'SET' : 'EMPTY',
            'merchant_id' => $this->merchantId ? 'SET' : 'EMPTY',
            'test_mode' => $this->testMode,
            'base_url' => $this->baseUrl
        ]);
    }

    /**
     * Check if a phone number is a test number
     */
    private function isTestNumber(string $phoneNumber): bool
    {
        $testNumbers = [
            '899817565',
            '888888888',
            '999999999',
            '123456789',
            '990000000'
        ];

        return in_array($phoneNumber, $testNumbers);
    }

    /**
     * Initialize a direct mobile money payment
     */
    public function initializeDirectPayment(
        float $amount,
        string $currency,
        string $reference,
        string $phoneNumber,
        string $operatorId,
        array $metadata = [],
        User $user = null
    ) {
        try {
            // Generate a unique charge ID for this transaction
            $chargeId = 'CHG-' . Str::uuid();

            $payload = [
                'amount' => $amount,
                'currency' => $currency,
                'mobile' => $phoneNumber,
                'mobile_money_operator_ref_id' => $operatorId,
                'ref_id' => $reference,
                'charge_id' => $chargeId,
                'meta' => $metadata,
            ];

            // Add user information if available
            if ($user) {
                $payload['email'] = $user->email;
                $payload['first_name'] = $user->first_name ?? explode(' ', $user->name)[0] ?? '';
                $payload['last_name'] = $user->last_name ?? (count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name)[1] : '');
            }

            Log::info('PayChangu direct payment payload', [
                'reference' => $reference,
                'phone_number' => $phoneNumber,
                'operator_id' => $operatorId,
                'amount' => $amount,
                'is_test_number' => $this->isTestNumber($phoneNumber),
                'test_mode' => $this->testMode,
                'secret_key_present' => !empty($this->secretKey),
                'secret_key_length' => strlen($this->secretKey ?? ''),
                'secret_key_first_10' => substr($this->secretKey ?? '', 0, 10),
                'base_url' => $this->baseUrl
            ]);

            if (empty($this->secretKey)) {
                Log::error('PayChangu secret key is empty', [
                    'api_key' => $this->apiKey ? 'SET' : 'EMPTY',
                    'merchant_id' => $this->merchantId ? 'SET' : 'EMPTY',
                    'test_mode' => $this->testMode
                ]);
                return [
                    'status' => false,
                    'message' => 'PayChangu secret key not configured',
                    'data' => null
                ];
            }

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/mobile-money/payments/initialize', $payload);

            Log::info('PayChangu API response received', [
                'status_code' => $response->status(),
                'response_body' => $response->body(),
                'reference' => $reference
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Check if the response indicates success
                if (isset($data['status']) && $data['status'] === 'success') {
                    return [
                        'status' => true,
                        'data' => $data,
                        'charge_id' => $data['data']['charge_id'] ?? $chargeId,
                        'is_test_number' => $this->isTestNumber($phoneNumber),
                    ];
                }

                Log::warning('PayChangu payment initialization returned non-success status', [
                    'response_data' => $data,
                    'reference' => $reference
                ]);

                return [
                    'status' => false,
                    'message' => $data['message'] ?? 'Payment initialization failed',
                    'data' => $data,
                ];
            }

            Log::error('PayChangu payment initialization failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'payload' => $payload,
            ]);

            return [
                'status' => false,
                'message' => 'Payment initialization failed',
                'data' => $response->json(),
            ];
        } catch (Exception $e) {
            Log::error('PayChangu payment initialization exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => false,
                'message' => 'Payment service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a direct charge payment using charge ID
     */
    public function verifyDirectCharge(string $chargeId)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/mobile-money/payments/' . $chargeId . '/verify');

            if ($response->successful()) {
                $data = $response->json();

                // Check if the response indicates success - handle multiple possible success indicators
                $isSuccessful = false;

                // Check top-level status
                if (isset($data['status']) && in_array($data['status'], ['success', 'successful'])) {
                    $isSuccessful = true;
                }

                // Check nested data status
                if (isset($data['data']['status']) && in_array($data['data']['status'], ['success', 'successful'])) {
                    $isSuccessful = true;
                }

                // Also check if message indicates success
                if (isset($data['message']) && str_contains(strtolower($data['message']), 'completed successfully')) {
                    $isSuccessful = true;
                }

                if ($isSuccessful) {
                    return [
                        'status' => true,
                        'data' => $data['data'] ?? $data,
                        'message' => $data['message'] ?? 'Payment verified successfully'
                    ];
                }

                return [
                    'status' => false,
                    'message' => $data['message'] ?? 'Payment verification failed',
                    'data' => $data,
                ];
            }

            Log::error('PayChangu direct charge verification failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'charge_id' => $chargeId,
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification failed',
                'data' => $response->json(),
            ];
        } catch (Exception $e) {
            Log::error('PayChangu direct charge verification exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'charge_id' => $chargeId,
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment using transaction reference
     * This method determines whether to use direct charge verification or standard verification
     */
    public function verifyPayment(string $transactionId)
    {
        try {
            // Find the transaction to get the charge_id
            $transaction = PaymentTransaction::where('transaction_id', $transactionId)->first();

            if (!$transaction) {
                return [
                    'status' => false,
                    'message' => 'Transaction not found',
                ];
            }

            // If we have a charge_id in metadata, use direct charge verification
            if (isset($transaction->metadata['charge_id'])) {
                return $this->verifyDirectCharge($transaction->metadata['charge_id']);
            }

            // Fallback to standard verification using transaction ID
            return $this->verifyStandardPayment($transactionId);

        } catch (\Exception $e) {
            Log::error('Payment verification error', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify standard payment using transaction reference
     */
    public function verifyStandardPayment(string $transactionId)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/verify', [
                'tx_ref' => $transactionId
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Check if the response indicates success - handle multiple possible success indicators
                $isSuccessful = false;

                // Check top-level status
                if (isset($data['status']) && in_array($data['status'], ['success', 'successful'])) {
                    $isSuccessful = true;
                }

                // Check nested data status
                if (isset($data['data']['status']) && in_array($data['data']['status'], ['success', 'successful'])) {
                    $isSuccessful = true;
                }

                // Also check if message indicates success
                if (isset($data['message']) && str_contains(strtolower($data['message']), 'completed successfully')) {
                    $isSuccessful = true;
                }

                if ($isSuccessful) {
                    return [
                        'status' => true,
                        'data' => $data['data'] ?? $data,
                        'message' => $data['message'] ?? 'Payment verified successfully'
                    ];
                }

                return [
                    'status' => false,
                    'message' => $data['message'] ?? 'Payment verification failed',
                    'data' => $data,
                ];
            }

            Log::error('PayChangu standard payment verification failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'transaction_id' => $transactionId,
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification failed',
                'data' => $response->json(),
            ];
        } catch (\Exception $e) {
            Log::error('PayChangu standard payment verification exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature according to PayChangu documentation
     * The signature is a SHA-256 HMAC hash of the webhook payload
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        if (empty($this->webhookSecret)) {
            Log::warning('PayChangu webhook secret not configured');
            return false;
        }

        // Generate SHA-256 HMAC hash of the webhook payload using the secret key
        $computedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);

        Log::debug('Webhook signature verification', [
            'computed_signature' => $computedSignature,
            'received_signature' => $signature,
            'payload_length' => strlen($payload)
        ]);

        // Compare the computed signature with the received signature
        return hash_equals($computedSignature, $signature);
    }



    /**
     * Process ticket purchase payment using direct charge
     */
    public function processTicketPurchase(
        array $tickets,
        User $user,
        string $phoneNumber,
        string $operatorId,
        VendorPaymentMethod $paymentMethod = null,
        string $existingReference = null
    ) {
        // Use existing reference if provided, otherwise generate new one
        if ($existingReference) {
            $reference = $existingReference;
            \Log::info('Using existing purchase reference', [
                'reference' => $reference,
                'user_id' => $user->id
            ]);
        } else {
            // Generate unique reference with multiple entropy sources
            $attempts = 0;
            $maxAttempts = 20;

            do {
                $attempts++;

                // Use multiple entropy sources for maximum uniqueness
                $microtime = microtime(true);
                $microseconds = (int)(($microtime - floor($microtime)) * 1000000);
                $randomString = strtoupper(Str::random(6));

                $reference = sprintf(
                    'TKT-%d-%d-%d-%s',
                    $user->id,
                    time(),
                    $microseconds,
                    $randomString
                );

                // Check if reference exists
                $exists = TicketPurchase::where('purchase_reference', $reference)->exists();

                if (!$exists) {
                    break;
                }

                // Add progressive delay to avoid rapid retries
                usleep($attempts * 1000); // Progressive delay: 1ms, 2ms, 3ms, etc.

            } while ($attempts < $maxAttempts);

            if ($attempts >= $maxAttempts) {
                throw new \Exception('Unable to generate unique purchase reference after ' . $maxAttempts . ' attempts');
            }

            \Log::info('Generated purchase reference', [
                'reference' => $reference,
                'user_id' => $user->id,
                'attempts' => $attempts
            ]);
        }

        $totalAmount = 0;
        $ticketDetails = [];

        foreach ($tickets as $ticketData) {
            $ticket = Ticket::find($ticketData['ticket_id']);
            if ($ticket) {
                $quantity = $ticketData['quantity'] ?? 1;
                $amount = $ticket->price * $quantity;
                $totalAmount += $amount;

                $ticketDetails[] = [
                    'ticket_id' => $ticket->id,
                    'ticket_name' => $ticket->name,
                    'quantity' => $quantity,
                    'unit_price' => $ticket->price,
                    'total_price' => $amount,
                    'event_id' => $ticket->event_id,
                ];
            }
        }

        if ($totalAmount <= 0) {
            return [
                'status' => false,
                'message' => 'Invalid ticket selection or pricing',
            ];
        }

        $result = $this->initializeDirectPayment(
            $totalAmount,
            'MWK',
            $reference,
            $phoneNumber,
            $operatorId,
            [
                'type' => 'ticket_purchase',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'tickets' => $ticketDetails,
                'total_tickets' => count($ticketDetails),
            ],
            $user
        );

        if (!empty($result['status']) && $result['status']) {

            \Log::info('PayChangu payment initiated successfully', [
                'reference' => $reference,
                'user_id' => $user->id,
                'total_amount' => $totalAmount,
                'ticket_count' => count($ticketDetails)
            ]);

            // Note: TicketPurchase records are created by the calling controller
            // This service only handles the payment processing with PayChangu

            $transaction = PaymentTransaction::create([
                'transaction_id' => $reference,
                'vendor_payment_method_id' => $paymentMethod?->id,
                'user_id' => $user->id,
                'amount' => $totalAmount,
                'currency' => 'MWK',
                'status' => 'pending',
                'payment_type' => 'ticket_purchase',
                'bookable_id' => $user->id,
                'bookable_type' => User::class,
                'metadata' => [
                    'tickets' => $ticketDetails,
                    'charge_id' => $result['charge_id'] ?? null,
                    'phone_number' => $phoneNumber,
                    'operator_id' => $operatorId,
                ],
                'gateway_response' => $result,
            ]);

            // For test numbers, verify payment immediately
            if (isset($result['is_test_number']) && $result['is_test_number']) {
                \App\Jobs\VerifyPaymentJob::dispatch($reference)->delay(now()->addSeconds(5));
            } else {
                \App\Jobs\VerifyPaymentJob::dispatch($reference)->delay(now()->addMinutes(2));
            }
            \App\Jobs\HandlePaymentTimeoutJob::dispatch($reference, 30);

            return [
                'status' => true,
                'message' => 'Ticket purchase payment initialized successfully',
                'data' => [
                    'reference' => $reference,
                    'charge_id' => $result['charge_id'] ?? null,
                    'total_amount' => $totalAmount,
                    'tickets' => $ticketDetails,
                ],
            ];
        }

        return [
            'status' => false,
            'message' => $result['message'] ?? 'Ticket purchase payment initialization failed',
            'data' => $result,
        ];
    }

    /**
     * Process ticket purchase payment using card
     */
    public function processTicketPurchaseWithCard(
        array $tickets,
        User $user,
        array $cardDetails,
        VendorPaymentMethod $paymentMethod = null,
        string $existingReference = null
    ) {
        if ($existingReference) {
            $reference = $existingReference;
        } else {
            $reference = 'TKT-' . Str::uuid();
        }

        $ticketDetails = [];
        $totalAmount = 0;

        foreach ($tickets as $ticketData) {
            $ticket = Ticket::find($ticketData['ticket_id']);
            if (!$ticket) {
                return [
                    'status' => false,
                    'message' => 'Ticket not found: ' . $ticketData['ticket_id'],
                ];
            }

            $quantity = $ticketData['quantity'];
            $ticketPrice = $ticket->tier->price;
            $subtotal = $ticketPrice * $quantity;
            $totalAmount += $subtotal;

            $ticketDetails[] = [
                'ticket_id' => $ticket->id,
                'tier_id' => $ticket->tier_id,
                'event_id' => $ticket->tier->event_id,
                'name' => $ticket->tier->name,
                'price' => $ticketPrice,
                'quantity' => $quantity,
                'subtotal' => $subtotal,
            ];
        }

        if ($totalAmount <= 0) {
            return [
                'status' => false,
                'message' => 'Invalid ticket selection or pricing',
            ];
        }

        $result = $this->initializeCardPayment(
            $totalAmount,
            'MWK',
            $reference,
            $cardDetails,
            [
                'type' => 'ticket_purchase',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'tickets' => $ticketDetails,
                'total_tickets' => count($ticketDetails),
            ],
            $user
        );

        if (!empty($result['status']) && $result['status']) {
            $transaction = PaymentTransaction::create([
                'transaction_id' => $reference,
                'vendor_payment_method_id' => $paymentMethod?->id,
                'user_id' => $user->id,
                'amount' => $totalAmount,
                'currency' => 'MWK',
                'status' => 'pending',
                'payment_type' => 'ticket_purchase',
                'bookable_id' => $user->id,
                'bookable_type' => User::class,
                'metadata' => [
                    'tickets' => $ticketDetails,
                    'charge_id' => $result['charge_id'] ?? null,
                    'card_last_four' => substr($cardDetails['number'], -4),
                    'payment_method' => 'card',
                ],
                'gateway_response' => $result,
            ]);

            return [
                'status' => true,
                'message' => 'Card payment initialized successfully',
                'data' => [
                    'reference' => $reference,
                    'charge_id' => $result['charge_id'] ?? null,
                ],
            ];
        }

        return [
            'status' => false,
            'message' => $result['message'] ?? 'Card payment initialization failed',
            'data' => $result,
        ];
    }

    /**
     * Process ticket purchase payment using bank account
     */
    public function processTicketPurchaseWithBankAccount(
        array $tickets,
        User $user,
        array $bankDetails,
        VendorPaymentMethod $paymentMethod = null,
        string $existingReference = null
    ) {
        if ($existingReference) {
            $reference = $existingReference;
        } else {
            $reference = 'TKT-' . Str::uuid();
        }

        $ticketDetails = [];
        $totalAmount = 0;

        foreach ($tickets as $ticketData) {
            $ticket = Ticket::find($ticketData['ticket_id']);
            if (!$ticket) {
                return [
                    'status' => false,
                    'message' => 'Ticket not found: ' . $ticketData['ticket_id'],
                ];
            }

            $quantity = $ticketData['quantity'];
            $ticketPrice = $ticket->tier->price;
            $subtotal = $ticketPrice * $quantity;
            $totalAmount += $subtotal;

            $ticketDetails[] = [
                'ticket_id' => $ticket->id,
                'tier_id' => $ticket->tier_id,
                'event_id' => $ticket->tier->event_id,
                'name' => $ticket->tier->name,
                'price' => $ticketPrice,
                'quantity' => $quantity,
                'subtotal' => $subtotal,
            ];
        }

        if ($totalAmount <= 0) {
            return [
                'status' => false,
                'message' => 'Invalid ticket selection or pricing',
            ];
        }

        $result = $this->initializeBankAccountPayment(
            $totalAmount,
            'MWK',
            $reference,
            $bankDetails,
            [
                'type' => 'ticket_purchase',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'tickets' => $ticketDetails,
                'total_tickets' => count($ticketDetails),
            ],
            $user
        );

        if (!empty($result['status']) && $result['status']) {
            $transaction = PaymentTransaction::create([
                'transaction_id' => $reference,
                'vendor_payment_method_id' => $paymentMethod?->id,
                'user_id' => $user->id,
                'amount' => $totalAmount,
                'currency' => 'MWK',
                'status' => 'pending',
                'payment_type' => 'ticket_purchase',
                'bookable_id' => $user->id,
                'bookable_type' => User::class,
                'metadata' => [
                    'tickets' => $ticketDetails,
                    'charge_id' => $result['charge_id'] ?? null,
                    'bank_name' => $bankDetails['bank_name'] ?? 'N/A',
                    'account_last_four' => substr($bankDetails['account_number'], -4),
                    'payment_method' => 'bank_account',
                ],
                'gateway_response' => $result,
            ]);

            return [
                'status' => true,
                'message' => 'Bank account payment initialized successfully',
                'data' => [
                    'reference' => $reference,
                    'charge_id' => $result['charge_id'] ?? null,
                ],
            ];
        }

        return [
            'status' => false,
            'message' => $result['message'] ?? 'Bank account payment initialization failed',
            'data' => $result,
        ];
    }

    /**
     * Process a payment for a booking using direct charge
     */
    public function processBookingPayment(
        $booking,
        VendorPaymentMethod $paymentMethod,
        $userId,
        string $phoneNumber,
        string $operatorId
    ) {
        $reference = 'BKG-' . Str::uuid();
        $user = User::find($userId);

        $result = $this->initializeDirectPayment(
            (float) $booking->total_price,
            'MWK',
            $reference,
            $phoneNumber,
            $operatorId,
            [
                'type' => 'booking',
                'booking_id' => $booking->id,
                'booking_type' => get_class($booking),
                'vendor_id' => $booking->vendor_id,
                'user_id' => $userId,
            ],
            $user
        );

        if (!empty($result['status']) && $result['status']) {
            $transaction = PaymentTransaction::create([
                'transaction_id' => $reference,
                'vendor_payment_method_id' => $paymentMethod->id,
                'user_id' => $userId,
                'amount' => $booking->total_price,
                'currency' => 'MWK',
                'status' => 'pending',
                'payment_type' => 'booking',
                'bookable_id' => $booking->id,
                'bookable_type' => get_class($booking),
                'metadata' => [
                    'charge_id' => $result['charge_id'] ?? null,
                    'phone_number' => $phoneNumber,
                    'operator_id' => $operatorId,
                ],
                'gateway_response' => $result,
            ]);

            // For test numbers, verify payment immediately
            if (isset($result['is_test_number']) && $result['is_test_number']) {
                \App\Jobs\VerifyPaymentJob::dispatch($reference)->delay(now()->addSeconds(5));
            } else {
                \App\Jobs\VerifyPaymentJob::dispatch($reference)->delay(now()->addMinutes(2));
            }
            \App\Jobs\HandlePaymentTimeoutJob::dispatch($reference, 30);

            return [
                'status' => true,
                'message' => 'Booking payment initialized successfully',
                'data' => [
                    'reference' => $reference,
                    'charge_id' => $result['charge_id'] ?? null,
                ],
            ];
        }

        return [
            'status' => false,
            'message' => $result['message'] ?? 'Booking payment initialization failed',
            'data' => $result,
        ];
    }

    /**
     * Direct mobile money charge
     */
    public function chargeMobileMoney(
        float $amount,
        string $currency,
        string $phoneNumber,
        string $operator,
        string $reference,
        array $metadata = []
    ) {
        try {
            $payload = [
                'amount' => $amount,
                'currency' => $currency,
                'tx_ref' => $reference,
                'mobile_money' => [
                    'phone' => $phoneNumber,
                    'operator' => $operator,
                ],
                'meta' => $metadata,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charge', $payload);

            if ($response->successful()) {
                return [
                    'status' => true,
                    'data' => $response->json(),
                ];
            }

            Log::error('PayChangu mobile money charge failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'payload' => $payload,
            ]);

            return [
                'status' => false,
                'message' => 'Mobile money charge failed',
                'data' => $response->json(),
            ];
        } catch (Exception $e) {
            Log::error('PayChangu mobile money charge exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => false,
                'message' => 'Mobile money charge error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get supported mobile money operators from PayChangu API
     */
    public function getMobileMoneyOperators()
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/mobile-money');

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'status' => true,
                    'data' => $data['data'] ?? $data,
                ];
            }

            Log::error('Failed to fetch mobile money operators', [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            // Fallback to hardcoded operators for Malawi
            return [
                'status' => true,
                'data' => [
                    [
                        'id' => 2,
                        'ref_id' => '20be6c20-adeb-4b5b-a7ba-0769820df4fb',
                        'name' => 'Airtel Money',
                        'country' => 'MW',
                        'currency' => 'MWK'
                    ],
                    [
                        'id' => 1,
                        'ref_id' => '27494cb5-ba9e-437f-a114-4e7a7686bcca',
                        'name' => 'TNM Mpamba',
                        'country' => 'MW',
                        'currency' => 'MWK'
                    ]
                ]
            ];
        } catch (Exception $e) {
            Log::error('Error fetching mobile money operators', [
                'error' => $e->getMessage(),
            ]);

            // Return fallback operators
            return [
                'status' => true,
                'data' => [
                    [
                        'id' => 2,
                        'ref_id' => '20be6c20-adeb-4b5b-a7ba-0769820df4fb',
                        'name' => 'Airtel Money',
                        'country' => 'MW',
                        'currency' => 'MWK'
                    ],
                    [
                        'id' => 1,
                        'ref_id' => '27494cb5-ba9e-437f-a114-4e7a7686bcca',
                        'name' => 'TNM Mpamba',
                        'country' => 'MW',
                        'currency' => 'MWK'
                    ]
                ]
            ];
        }
    }

    /**
     * Initialize a bank transfer withdrawal (supports both banks and mobile money)
     */
    public function initializeBankTransferWithdrawal(
        float $amount,
        string $currency,
        string $reference,
        array $bankDetails,
        array $metadata = [],
        User $user = null
    ) {
        try {
            $payload = [
                'payout_method' => 'bank_transfer',
                'amount' => $amount,
                'currency' => $currency,
                'charge_id' => $reference,
                'bank_uuid' => $bankDetails['bank_uuid'] ?? '', // Will be set based on bank
                'bank_account_name' => $bankDetails['account_name'],
                'bank_account_number' => $bankDetails['account_number'],
                'meta' => $metadata,
            ];

            if ($user) {
                $payload['email'] = $user->email;
                $payload['first_name'] = $user->first_name ?? explode(' ', $user->name)[0] ?? '';
                $payload['last_name'] = $user->last_name ?? (count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name)[1] : '');
            }

            Log::info('PayChangu bank transfer withdrawal payload', [
                'reference' => $reference,
                'amount' => $amount,
                'bank_name' => $bankDetails['bank_name'] ?? 'N/A',
                'account_number' => substr($bankDetails['account_number'], -4) // Log only last 4 digits
            ]);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/direct-charge/payouts/initialize', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'status' => true,
                    'data' => $data,
                    'withdrawal_id' => $data['data']['transaction']['trans_id'] ?? null,
                    'reference' => $reference,
                ];
            }

            Log::error('PayChangu bank transfer withdrawal failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Bank transfer withdrawal initialization failed',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu bank transfer withdrawal exception', [
                'message' => $e->getMessage(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Bank transfer withdrawal error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Initialize a mobile money withdrawal (uses mobile money specific endpoint)
     */
    public function initializeMobileMoneyWithdrawal(
        float $amount,
        string $currency,
        string $reference,
        string $phoneNumber,
        string $operatorId,
        array $metadata = [],
        User $user = null
    ) {
        try {
            // Get the mobile money operator reference ID
            $operatorRefId = $this->getMobileMoneyOperatorRefId($operatorId);

            $payload = [
                'mobile_money_operator_ref_id' => $operatorRefId,
                'mobile' => $phoneNumber,
                'amount' => (string) $amount,
                'charge_id' => $reference,
            ];

            // Add optional user details
            if ($user) {
                $payload['email'] = $user->email;
                $payload['first_name'] = $user->first_name ?? explode(' ', $user->name)[0] ?? '';
                $payload['last_name'] = $user->last_name ?? (count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name)[1] : '');
            }

            Log::info('PayChangu mobile money withdrawal payload', [
                'reference' => $reference,
                'amount' => $amount,
                'phone_number' => $phoneNumber,
                'operator_id' => $operatorId,
                'operator_ref_id' => $operatorRefId
            ]);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/mobile-money/payments/initialize', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'status' => true,
                    'data' => $data,
                    'withdrawal_id' => $data['data']['trans_id'] ?? null,
                    'reference' => $reference,
                ];
            }

            Log::error('PayChangu mobile money withdrawal failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Mobile money withdrawal initialization failed',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu mobile money withdrawal exception', [
                'message' => $e->getMessage(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Mobile money withdrawal error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get the mobile money operator reference ID (for mobile money specific endpoint)
     */
    private function getMobileMoneyOperatorRefId(string $operatorId): string
    {
        // These are the operator reference IDs for the mobile money specific endpoint
        $operatorRefIds = [
            'tnm_mpamba' => '27494cb5-ba9e-437f-a114-4e7a7686bcca', // TNM Mpamba
            'airtel_money' => '20be6c20-adeb-4b5b-a7ba-0769820df4fb', // Airtel Money
        ];

        return $operatorRefIds[$operatorId] ?? $operatorRefIds['airtel_money']; // Default to Airtel
    }

    /**
     * Verify withdrawal status
     */
    public function verifyWithdrawal(string $withdrawalId)
    {
        try {
            // For bank payouts, use the single bank payout details endpoint
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/direct-charge/payouts/' . $withdrawalId);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'status' => true,
                    'data' => $data,
                ];
            }

            return [
                'status' => false,
                'message' => 'Withdrawal verification failed',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu withdrawal verification exception', [
                'message' => $e->getMessage(),
                'withdrawal_id' => $withdrawalId
            ]);

            return [
                'status' => false,
                'message' => 'Withdrawal verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle withdrawal webhook
     */
    public function handleWithdrawalWebhook(array $data)
    {
        try {
            $withdrawalId = $data['withdrawal_id'] ?? null;
            $status = $data['status'] ?? null;
            $reference = $data['ref_id'] ?? null;

            if (!$withdrawalId || !$status || !$reference) {
                throw new \Exception('Invalid withdrawal webhook data');
            }

            $withdrawal = \App\Models\Withdrawal::where('reference', $reference)->first();

            if (!$withdrawal) {
                Log::warning('Withdrawal not found for webhook', ['reference' => $reference]);
                return false;
            }

            $statusMapping = [
                'completed' => 'completed',
                'success' => 'completed',
                'successful' => 'completed',
                'failed' => 'failed',
                'pending' => 'processing',
                'processing' => 'processing',
            ];

            $newStatus = $statusMapping[strtolower($status)] ?? 'failed';

            $withdrawal->update([
                'status' => $newStatus,
                'gateway_reference' => $withdrawalId,
                'processed_at' => now(),
                'notes' => "Updated via webhook: {$status}"
            ]);

            if ($newStatus === 'completed') {
                $hostBalance = \App\Models\HostBalance::getOrCreateForUser($withdrawal->user_id);
                $hostBalance->decrement('pending_balance', (float)$withdrawal->amount);
                $hostBalance->increment('total_withdrawn', (float)$withdrawal->net_amount);
            } elseif ($newStatus === 'failed') {
                $hostBalance = \App\Models\HostBalance::getOrCreateForUser($withdrawal->user_id);
                $hostBalance->increment('available_balance', (float)$withdrawal->amount);
                $hostBalance->decrement('pending_balance', (float)$withdrawal->amount);
            }

            if (class_exists('\App\Events\WithdrawalStatusUpdated')) {
                event(new \App\Events\WithdrawalStatusUpdated($withdrawal));
            }

            Log::info('Withdrawal webhook processed', [
                'withdrawal_id' => $withdrawal->id,
                'reference' => $reference,
                'old_status' => $withdrawal->getOriginal('status'),
                'new_status' => $newStatus
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Withdrawal webhook processing failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return false;
        }
    }

    /**
     * Get list of banks and mobile money providers
     */
    public function getBanks()
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/get-banks');

            if ($response->successful()) {
                return [
                    'status' => true,
                    'data' => $response->json(),
                ];
            }

            Log::error('PayChangu get-banks failed', [
                'status' => $response->status(),
                'response' => $response->json()
            ]);

            return [
                'status' => false,
                'message' => 'Failed to fetch banks',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu get-banks exception', [
                'message' => $e->getMessage()
            ]);

            return [
                'status' => false,
                'message' => 'Error fetching banks: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Initialize a card payment using direct charge
     */
    public function initializeCardPayment(
        float $amount,
        string $currency,
        string $reference,
        array $cardDetails,
        array $metadata = [],
        User $user = null
    ) {
        try {
            $chargeId = 'CHG-' . Str::uuid();

            $payload = [
                'amount' => $amount,
                'currency' => $currency,
                'ref_id' => $reference,
                'charge_id' => $chargeId,
                'card' => [
                    'number' => $cardDetails['number'],
                    'expiry_month' => $cardDetails['expiry_month'],
                    'expiry_year' => $cardDetails['expiry_year'],
                    'cvv' => $cardDetails['cvv'],
                ],
                'meta' => $metadata,
            ];

            if ($user) {
                $payload['email'] = $user->email;
                $payload['first_name'] = $user->first_name ?? explode(' ', $user->name)[0] ?? '';
                $payload['last_name'] = $user->last_name ?? (count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name)[1] : '');
            }

            Log::info('PayChangu card payment payload', [
                'reference' => $reference,
                'amount' => $amount,
                'card_last_four' => substr($cardDetails['number'], -4)
            ]);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/card/payments/initialize', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'status' => true,
                    'data' => $data,
                    'charge_id' => $chargeId,
                    'reference' => $reference,
                ];
            }

            Log::error('PayChangu card payment failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Card payment initialization failed',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu card payment exception', [
                'message' => $e->getMessage(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Card payment error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Initialize a bank account payment using direct charge
     */
    public function initializeBankAccountPayment(
        float $amount,
        string $currency,
        string $reference,
        array $bankDetails,
        array $metadata = [],
        User $user = null
    ) {
        try {
            $chargeId = 'CHG-' . Str::uuid();

            $payload = [
                'amount' => $amount,
                'currency' => $currency,
                'ref_id' => $reference,
                'charge_id' => $chargeId,
                'bank_account' => [
                    'bank_uuid' => $bankDetails['bank_uuid'],
                    'account_number' => $bankDetails['account_number'],
                    'account_name' => $bankDetails['account_name'],
                ],
                'meta' => $metadata,
            ];

            if ($user) {
                $payload['email'] = $user->email;
                $payload['first_name'] = $user->first_name ?? explode(' ', $user->name)[0] ?? '';
                $payload['last_name'] = $user->last_name ?? (count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name)[1] : '');
            }

            Log::info('PayChangu bank account payment payload', [
                'reference' => $reference,
                'amount' => $amount,
                'bank_name' => $bankDetails['bank_name'] ?? 'N/A',
                'account_last_four' => substr($bankDetails['account_number'], -4)
            ]);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl . '/bank-account/payments/initialize', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'status' => true,
                    'data' => $data,
                    'charge_id' => $chargeId,
                    'reference' => $reference,
                ];
            }

            Log::error('PayChangu bank account payment failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Bank account payment initialization failed',
                'data' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayChangu bank account payment exception', [
                'message' => $e->getMessage(),
                'reference' => $reference
            ]);

            return [
                'status' => false,
                'message' => 'Bank account payment error: ' . $e->getMessage(),
            ];
        }
    }
}
